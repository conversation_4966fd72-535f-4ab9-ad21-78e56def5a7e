package com.sq.auth;
import android.app.Dialog;
import android.content.pm.ActivityInfo;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Button;
import com.mobile.auth.gatewayauth.AuthRegisterXmlConfig;
import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.AuthRegisterViewConfig;
import com.mobile.auth.gatewayauth.ui.AbstractPnsViewDelegate;
import com.sq.dlyz_flutter.R;
import com.sq.dlyz_flutter.DouluoManagerChannel;
import com.sq.dlyz_flutter.ActivityStackManager;

/**
 * 一键登录UI配置管理器
 * 提供自定义的UI样式配置和交互处理
 * 重构为全屏页面模式，提升用户体验
 */
public class FastLoginUIConfig {

    private static final String TAG = "FastLoginUIConfig";

    /**
     * 创建一键登录UI配置
     * 全屏页面模式配置，支持导航栏和自定义布局
     */
    public static AuthUIConfig createCustomUIConfig(Context context) {
        int unit = 5;

        // 从FastLoginManager获取协议地址
        FastLoginManager manager = FastLoginManager.getInstance(context);
        String userAgreementUrl = manager.getUserAgreementUrl();
        String privacyPolicyUrl = manager.getPrivacyPolicyUrl();

        return new AuthUIConfig.Builder().setAppPrivacyOne("《用户协议》", userAgreementUrl)
            .setAppPrivacyTwo("《隐私政策》", privacyPolicyUrl)
            .setAppPrivacyColor(Color.GRAY, Color.parseColor("#4571FB")).setPrivacyConectTexts(new String[]{"及"})
            .setPrivacyTextSizeDp(12)

            //设置运营商协议显示位置
            .setPrivacyOperatorIndex(2).setPrivacyState(false).setPrivacyOffsetY_B(20)
            .setStatusBarColor(Color.TRANSPARENT).setStatusBarUIFlag(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN)
            .setNavHidden(true)                     // 隐藏导航栏，为轮播图腾出空间

            .setPageBackgroundPath("wechat_mini_game_sheet_bg")  // 页面背景为白色
            .setLogoHidden(true)                    // 隐藏logo
            .setSloganHidden(false)
            .setSloganTextSizeDp(12)
            .setSloganOffsetY(270)// 隐藏slogan

            // === 手机号码显示配置 ===
            .setNumFieldOffsetY(230)                // 手机号距离顶部位置
            .setNumberSizeDp(33)                    // 手机号文字大小
            .setNumberColor(Color.parseColor("#FF212121"))  // 手机号文字颜色

            // === 登录按钮配置 ===             // 按钮宽度（全屏模式下稍微增大）
            .setLogBtnHeight(50)                    // 按钮高度
            .setLogBtnMarginLeftAndRight(16)        // 按钮左右边距
            .setLogBtnTextSizeDp(17)                // 按钮文字大小
            .setLogBtnText("本机号码一键登录")        // 按钮文字
            .setLogBtnTextColor(Color.WHITE)        // 按钮文字颜色
            .setLogBtnBackgroundPath("sysq_dialog_login_btn_bg")  // 按钮背景
            .setLogBtnOffsetY(320)                  // 按钮距离顶部位置
            .setSwitchAccHidden(true)
            // === 隐私协议配置 ===
            .setVendorPrivacyPrefix("《").setVendorPrivacySuffix("》").setCheckboxHidden(false)               // 显示复选框
            .setCheckedImgDrawable(context.getResources().getDrawable(R.drawable.sysq_dialog_login_privacy_check_box))

            // === 动画配置 ===
            .setAuthPageActIn("in_activity", "out_activity")   // 进入动画
            .setAuthPageActOut("in_activity", "out_activity")  // 退出动画

            .setScreenOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)  // 竖屏显示
            .setLogBtnToastHidden(true)             // 隐藏Toast提示
            .setProtocolAction(context.getPackageName() + ".protocolWeb")  // 协议页面Action
            .create();
    }

    /**
     * dp转px工具方法
     */
    private static int dip2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue,
            context.getResources().getDisplayMetrics());
    }

    /**
     * 从 Flutter 资源中加载 Bitmap
     */
    private static Bitmap loadFlutterAssetBitmap(Context context, String assetName) {
        try {
            AssetManager am = context.getAssets();
            // Flutter 打包后资源前缀为 flutter_assets/
            String fullPath = "flutter_assets/assets/images/" + assetName;
            return BitmapFactory.decodeStream(am.open(fullPath));
        } catch (Exception e) {
            Log.e(TAG, "加载Flutter资源失败: " + assetName, e);
            return null;
        }
    }


    /**
     * 底部三按钮区域：微信登录、账号密码登录、其他手机号登录
     * 如果授权应用列表为空，则不显示微信登录按钮
     */
    private static View initBottomActionBar(Context context, DouluoManagerChannel channel, boolean hasAuthApps, PhoneNumberAuthHelper authHelper) {
        // 获取协议URL
        FastLoginManager manager = FastLoginManager.getInstance(context);
        String userAgreementUrl = manager.getUserAgreementUrl();
        String privacyPolicyUrl = manager.getPrivacyPolicyUrl();
        
        LinearLayout bar = new LinearLayout(context);
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT);
        lp.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
        lp.setMargins(0, 0, 0, dip2px(context, 80));
        bar.setLayoutParams(lp);
        bar.setOrientation(LinearLayout.HORIZONTAL);

        // 仅在有授权应用时添加微信登录按钮
        if (hasAuthApps) {
            View wechatItem = createLoginItem(context, "微信登录", "login_wechat.png", new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    boolean checked = authHelper.queryCheckBoxIsChecked();
                    if (!checked) {
                        // 从ActivityStackManager获取当前Activity作为Context
                        Context activityContext = ActivityStackManager.getInstance().getTopActivity();
                        showUserAgreementDialog(activityContext != null ? activityContext : context, 
                                              userAgreementUrl, privacyPolicyUrl, channel, authHelper);
                    } else {
                        if (channel != null) {
                            channel.onFastLoginWeChatClicked();
                        }
                    }

                }
            });
            bar.addView(wechatItem);
            Log.d(TAG, "已添加微信登录按钮");
        } else {
            Log.d(TAG, "授权应用列表为空，不显示微信登录按钮");
        }

        // 添加账号密码登录按钮
        View accountItem = createLoginItem(context, "账号密码登录", "login_account.png", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (channel != null) {
                    channel.onFastLoginAccountLoginClicked();
                }
            }
        });
        bar.addView(accountItem);

        return bar;
    }

    private static View createLoginItem(Context context, String label, String assetName, View.OnClickListener onClick) {
        LinearLayout item = new LinearLayout(context);
        LinearLayout.LayoutParams ilp = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT);
        ilp.setMargins(dip2px(context, 20), 0, dip2px(context, 20), 0);
        item.setLayoutParams(ilp);
        item.setOrientation(LinearLayout.VERTICAL);
        item.setGravity(Gravity.CENTER);

        ImageView icon = new ImageView(context);
        LinearLayout.LayoutParams imgLp = new LinearLayout.LayoutParams(dip2px(context, 56), dip2px(context, 56));
        icon.setLayoutParams(imgLp);
        Bitmap bmp = loadFlutterAssetBitmap(context, assetName);
        if (bmp != null) {
            icon.setImageBitmap(bmp);
        }

        TextView text = new TextView(context);
        LinearLayout.LayoutParams tlp = new LinearLayout.LayoutParams(LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT);
        tlp.setMargins(0, dip2px(context, 6), 0, 0);
        text.setLayoutParams(tlp);
        text.setText(label);
        text.setTextColor(Color.parseColor("#666666"));
        text.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f);

        item.setOnClickListener(onClick);
        item.addView(icon);
        item.addView(text);
        return item;
    }

    /**
     * 一键登录下方的“其他手机号登录”按钮（描边）
     */
    private static View initOtherPhoneButton(Context context, DouluoManagerChannel channel) {
        TextView btn = new TextView(context);
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(LayoutParams.MATCH_PARENT
            , dip2px(context, 50));
        lp.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        lp.setMargins(dip2px(context, 16), dip2px(context, 390), dip2px(context, 16), 0); // 位于一键登录按钮下方
        btn.setLayoutParams(lp);
        btn.setText("其他手机号登录");
        btn.setGravity(Gravity.CENTER);
        btn.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f);
        btn.setTextColor(Color.parseColor("#303133"));

        GradientDrawable border = new GradientDrawable();
        border.setColor(Color.TRANSPARENT);
        border.setCornerRadius(dip2px(context, 8));
        border.setStroke(dip2px(context, 1), Color.parseColor("#ff303133"));
        btn.setBackground(border);

        btn.setOnClickListener(v -> {
            if (channel != null) {
                channel.onFastLoginOtherPhoneClicked();
            }
        });
        return btn;
    }

    /**
     * 配置一键登录界面样式
     * 设置轮播图、自定义视图配置
     */
    public static void configAuthPage(final PhoneNumberAuthHelper authHelper, Context context, DouluoManagerChannel channel, boolean hasAuthApps) {
        try {
            // 清理之前的配置
            authHelper.removeAuthRegisterXmlConfig();
            authHelper.removeAuthRegisterViewConfig();

            authHelper.addAuthRegisterXmlConfig(
                new AuthRegisterXmlConfig.Builder().setLayout(R.layout.layout_auth_top, new AbstractPnsViewDelegate() {
                    @Override
                    public void onViewCreated(View view) {

                    }
                }).build());

            // 底部按钮（微信/账号密码）- 根据授权应用列表决定是否显示微信按钮
            authHelper.addAuthRegistViewConfig("bottom_actions",
                new AuthRegisterViewConfig.Builder().setView(initBottomActionBar(context, channel, hasAuthApps, authHelper))
                    .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY).build());

            // 一键登录下方的"其他手机号登录"
            authHelper.addAuthRegistViewConfig("other_phone_btn",
                new AuthRegisterViewConfig.Builder().setView(initOtherPhoneButton(context, channel))
                    .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY).build());
            Log.d(TAG, "自定义按钮组配置成功，hasAuthApps=" + hasAuthApps);

            // 设置基础AuthUIConfig
            AuthUIConfig uiConfig = createCustomUIConfig(context);
            authHelper.setAuthUIConfig(uiConfig);

            Log.d(TAG, "闪验页面配置完成（包含轮播图）");
        } catch (Exception e) {
            Log.e(TAG, "配置一键登录UI失败", e);
        }
    }

    /**
     * 显示用户协议提示弹窗，使用自定义Dialog避免主题冲突
     */
    private static void showUserAgreementDialog(Context context, String userAgreementUrl, String privacyPolicyUrl, DouluoManagerChannel channel, final PhoneNumberAuthHelper authHelper) {
        // 创建自定义Dialog
        Dialog dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setCancelable(false);
        
        // 创建自定义布局
        LinearLayout dialogLayout = new LinearLayout(context);
        dialogLayout.setOrientation(LinearLayout.VERTICAL);
        dialogLayout.setBackgroundColor(Color.WHITE);
        
        // 设置圆角背景
        GradientDrawable background = new GradientDrawable();
        background.setColor(Color.WHITE);
        background.setCornerRadius(dip2px(context, 16));
        dialogLayout.setBackground(background);
        
        // 设置外层容器，添加半透明遮罩效果
        RelativeLayout containerLayout = new RelativeLayout(context);
        containerLayout.setBackgroundColor(Color.TRANSPARENT); // 完全透明背景
        containerLayout.setGravity(Gravity.CENTER);
        
        // 设置dialog布局的外边距，实现居中效果
        RelativeLayout.LayoutParams dialogParams = new RelativeLayout.LayoutParams(
            (int) (context.getResources().getDisplayMetrics().widthPixels * 0.85),
            LayoutParams.WRAP_CONTENT);
        dialogParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        dialogLayout.setLayoutParams(dialogParams);
        
        // 标题
        TextView title = new TextView(context);
        title.setText("服务协议及隐私保护");
        title.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
        title.setTextColor(Color.parseColor("#333333"));
        title.setGravity(Gravity.CENTER);
        title.setTypeface(title.getTypeface(), android.graphics.Typeface.BOLD);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        titleParams.setMargins(0, dip2px(context, 24), 0, dip2px(context, 20));
        title.setLayoutParams(titleParams);
        dialogLayout.addView(title);
        
        // 协议内容
        TextView content = new TextView(context);
        String text = "为了更好地保障你的合法权益，请你先阅读并同意《用户协议》和《隐私政策》，未注册的手机号将自动完成账号注册";
        
        // 创建可点击的文本
        SpannableString spannableString = new SpannableString(text);
        
        // 用户协议链接
        int userAgreementStart = text.indexOf("《用户协议》");
        int userAgreementEnd = userAgreementStart + "《用户协议》".length();
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                Log.d(TAG, "点击用户协议");
                if (userAgreementUrl != null && !userAgreementUrl.isEmpty()) {
                    // 使用ProtocolWebActivity展示用户协议
                    String finalUrl = userAgreementUrl.contains("isAgree=true") ? userAgreementUrl : userAgreementUrl + "&isAgree=true&env=dl";
                    Intent intent = new Intent(context, ProtocolWebActivity.class);
                    intent.putExtra("url", finalUrl);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(intent);
                }
            }
            
            @Override
            public void updateDrawState(android.text.TextPaint ds) {
                super.updateDrawState(ds);
                ds.setUnderlineText(false); // 移除下划线
                ds.setColor(Color.parseColor("#6B73FF")); // 保持原色，不变化
            }
        }, userAgreementStart, userAgreementEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#6B73FF")), 
            userAgreementStart, userAgreementEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        
        // 隐私政策链接
        int privacyStart = text.indexOf("《隐私政策》");
        int privacyEnd = privacyStart + "《隐私政策》".length();
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                Log.d(TAG, "点击隐私政策");
                if (privacyPolicyUrl != null && !privacyPolicyUrl.isEmpty()) {
                    // 使用ProtocolWebActivity展示隐私政策
                    String finalUrl = privacyPolicyUrl.contains("isAgree=true") ? privacyPolicyUrl : privacyPolicyUrl + "&isAgree=true&env=dl";
                    Intent intent = new Intent(context, ProtocolWebActivity.class);
                    intent.putExtra("url", finalUrl);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(intent);
                }
            }
            
            @Override
            public void updateDrawState(android.text.TextPaint ds) {
                super.updateDrawState(ds);
                ds.setUnderlineText(false); // 移除下划线
                ds.setColor(Color.parseColor("#6B73FF")); // 保持原色，不变化
            }
        }, privacyStart, privacyEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#6B73FF")), 
            privacyStart, privacyEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        
        content.setText(spannableString);
        content.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        content.setTextColor(Color.parseColor("#666666"));
        content.setLineSpacing(dip2px(context, 2), 1.6f);
        content.setMovementMethod(LinkMovementMethod.getInstance());
        content.setHighlightColor(Color.TRANSPARENT); // 移除点击高亮背景
        LinearLayout.LayoutParams contentParams = new LinearLayout.LayoutParams(
            LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        contentParams.setMargins(dip2px(context, 24), 0, dip2px(context, 24), dip2px(context, 20));
        content.setLayoutParams(contentParams);
        dialogLayout.addView(content);
        
        // 分割线
        View divider = new View(context);
        divider.setBackgroundColor(Color.parseColor("#EEEEEE"));
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
            LayoutParams.MATCH_PARENT, dip2px(context, 1));
        divider.setLayoutParams(dividerParams);
        dialogLayout.addView(divider);
        
        // 按钮区域
        LinearLayout buttonLayout = new LinearLayout(context);
        buttonLayout.setOrientation(LinearLayout.HORIZONTAL);
        LinearLayout.LayoutParams buttonLayoutParams = new LinearLayout.LayoutParams(
            LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        buttonLayout.setLayoutParams(buttonLayoutParams);
        
        // 不同意按钮
        Button disagreeButton = new Button(context);
        disagreeButton.setText("不同意");
        disagreeButton.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        disagreeButton.setTextColor(Color.parseColor("#999999"));
        disagreeButton.setBackground(null);
        LinearLayout.LayoutParams disagreeParams = new LinearLayout.LayoutParams(
            0, dip2px(context, 54), 1);
        disagreeButton.setLayoutParams(disagreeParams);
        
        // 中间分割线
        View middleDivider = new View(context);
        middleDivider.setBackgroundColor(Color.parseColor("#EEEEEE"));
        LinearLayout.LayoutParams middleDividerParams = new LinearLayout.LayoutParams(
            dip2px(context, 1), dip2px(context, 54));
        middleDivider.setLayoutParams(middleDividerParams);
        
        // 同意并继续按钮
        Button agreeButton = new Button(context);
        agreeButton.setText("同意并继续");
        agreeButton.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        agreeButton.setTextColor(Color.parseColor("#6B73FF"));
        agreeButton.setBackground(null);
        LinearLayout.LayoutParams agreeParams = new LinearLayout.LayoutParams(
            0, dip2px(context, 54), 1);
        agreeButton.setLayoutParams(agreeParams);
        
        buttonLayout.addView(disagreeButton);
        buttonLayout.addView(middleDivider);
        buttonLayout.addView(agreeButton);
        dialogLayout.addView(buttonLayout);
        
        // 将dialog布局添加到容器中
        containerLayout.addView(dialogLayout);
        
        // 设置按钮点击事件
        disagreeButton.setOnClickListener(v -> {
            dialog.dismiss();
        });
        
        agreeButton.setOnClickListener(v -> {
            if (authHelper != null) {
                authHelper.setProtocolChecked(true);
            }
            dialog.dismiss();
            // 用户同意协议后，继续执行微信登录
            if (channel != null) {
                channel.onFastLoginWeChatClicked();
            }
        });
        
        // 设置Dialog内容
        dialog.setContentView(containerLayout);
        
        // 设置Dialog窗口属性
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(android.R.color.transparent);
            window.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
            window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, 
                           WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        }
        dialog.show();
    }
}