package com.sq.dlyz_flutter;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.sq.dlyz_flutter.oaid.IdsHelper;

public class DLApplication extends Application {
    private static final String TAG = "MyApplication";

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Application onCreate");
        
        // 初始化OAID SDK
        IdsHelper.attachBaseContext(this);
        
        // 注册Activity生命周期回调来管理Activity栈
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
                Log.d(TAG, "Activity created: " + activity.getClass().getSimpleName());
                ActivityStackManager.getInstance().addActivity(activity);
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                Log.d(TAG, "Activity started: " + activity.getClass().getSimpleName());
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                Log.d(TAG, "Activity resumed: " + activity.getClass().getSimpleName());
                // 确保当前Activity在栈顶
                ActivityStackManager.getInstance().addActivity(activity);
                
                // 检查是否是闪验LoginAuthActivity可见
                if ("com.mobile.auth.gatewayauth.LoginAuthActivity".equals(activity.getClass().getName())) {
                    Log.d(TAG, "闪验LoginAuthActivity可见，开始检查授权状态");
                    // 调用Flutter的checkAuthAppStatusIfNeeded方法
                    DouluoManagerChannel douluoManager = DouluoManagerChannel.getInstance(DLApplication.this);
                    if (douluoManager != null) {
                        douluoManager.checkAuthAppStatusIfNeeded();
                    }
                }
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
                Log.d(TAG, "Activity paused: " + activity.getClass().getSimpleName());
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                Log.d(TAG, "Activity stopped: " + activity.getClass().getSimpleName());
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
                Log.d(TAG, "Activity save instance state: " + activity.getClass().getSimpleName());
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                Log.d(TAG, "Activity destroyed: " + activity.getClass().getSimpleName());
                ActivityStackManager.getInstance().removeActivity(activity);
            }
        });
    }
}