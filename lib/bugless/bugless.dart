import 'dart:io';

import 'package:dlyz_flutter/bugless/bugless_info.dart';
import 'package:dlyz_flutter/net/http_service.dart';
import 'package:dlyz_flutter/net/sign_interceptor.dart';
import 'package:flutter/cupertino.dart';

import '../net/config/http_base_config.dart';
import '../net/transformers/direct_data_strategy.dart';
import '../utils/md5_utils.dart';

class Bugless {
  static final _androidOsName = 'android';
  static final _androidBuglessAppId = 'TlSR2VL0Xf';
  static final _androidBuglessAppSecret = '0a9d113d81556b99c006971f659922e6';

  static final _iosOsName = 'ios';
  static final _iosBuglessAppId = 'kAw6gHGzjy';
  static final _iosBuglessAppSecret = '71215bfdd886040dbfd394c9288611d7';

  factory Bugless() => _instance;

  Bugless._internal();

  static final Bugless _instance = Bugless._internal();

  static Bugless getInstance() {
    return _instance;
  }

  /// 崩溃上报
  Future<void> crashReport(
    int actionType,
    String title,
    String msg,
    int htime,
  ) async {
    var contentJson = {"title": title, "msg": msg, "stack": msg, "stackMD5": Md5Utils.generateMd5(msg)};
    var inputParams = {
      'type': 1,
      'content': contentJson,
      'actionType': actionType,
      'htime': htime,
    };
    var commonParams = _getCommonParams();
    var mergeParams = {...commonParams, ...inputParams};
    debugPrint('bugless crashReport params: $mergeParams');
    var response = await HttpService.getInstance().post<BuglessInfo>(
      '/api/bugless',
      baseUrl: HttpBaseConfig.buglessUrl,
      signType: SignType.v4,
      data: mergeParams,
      parseStrategy: DirectDataStrategy(),
      fromJsonT: (json) {
        return BuglessInfo.fromJson(json);
      },
    );
    if (response.success && response.data?.code == 0) {
      debugPrint('bugless crashReport success');
    } else {
      debugPrint('bugless crashReport error: ${response.message}');
    }
  }

  Map<String, String> _getCommonParams() {
    var isAndroid = Platform.isAndroid;
    return {
      'os': isAndroid ? _androidOsName : _iosOsName,
      'appid': isAndroid ? _androidBuglessAppId : _iosBuglessAppId,
      'apiversion': 'v1',
      'appsecret': isAndroid ? _androidBuglessAppSecret : _iosBuglessAppSecret,
    };
  }
}
