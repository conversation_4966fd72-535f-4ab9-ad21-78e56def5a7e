import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../providers/download_provider.dart';
import '../../model/download_info.dart';
import '../../services/download/ALDownloader.dart';
import '../../services/download/ALDownloaderStatus.dart';
import '../../services/download/ALDownloaderHandlerInterface.dart';
import '../../services/download/ALDownloaderFileManager.dart';
import '../../services/download/ALDownloaderTypeDefine.dart';
import '../../manager/channel_manager.dart';
import '../../components/common_alert.dart';

class VersionUpdateDialog extends StatefulWidget {
  final bool? exitAppOnClose; // true: 退出应用；false: 仅关闭弹窗
  final VoidCallback? onUpdateNow;
  final String? downloadUrl;  // 添加下载链接参数
  final String? message;      // 自定义提示文案
  final String? version;      // 自定义版本

  const VersionUpdateDialog({
    super.key,
    this.exitAppOnClose,
    this.onUpdateNow,
    this.downloadUrl,  // 下载链接
    this.message,
    this.version,
  });

  @override
  State<VersionUpdateDialog> createState() => _VersionUpdateDialogState();
}

class _VersionUpdateDialogState extends State<VersionUpdateDialog> {
  bool _isDownloading = false;
  bool _isDownloadCompleted = false;  // 下载完成状态
  double _downloadProgress = 0.0;
  DownloadInfo? _downloadInfo;  // 下载信息对象
  ALDownloaderHandlerInterfaceId? _handlerInterfaceId;  // 处理器接口ID
  String? _downloadedFilePath;  // 下载完成的文件路径

  @override
  void initState() {
    super.initState();
  }

  /// 计算文本高度，用于动态调整弹窗大小
  double _calculateTextHeight(String text, double maxWidth, TextStyle style) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
      maxLines: null,
    );
    textPainter.layout(maxWidth: maxWidth);
    return textPainter.height;
  }

  void _cleanupIfDownloading() {
    try {
      // 若未下载完成，则取消并清理未完成的数据缓存
      if (!_isDownloadCompleted && widget.downloadUrl != null) {
        // 取消会删除未完成的数据
        ALDownloader.cancel(widget.downloadUrl!);
      }
      // 清理下载回调
      if (_handlerInterfaceId != null) {
        ALDownloader.removeHandlerInterfaceForId(_handlerInterfaceId!);
        _handlerInterfaceId = null;
      }
    } catch (_) {}
  }

  void _startDownload() {
    if (widget.downloadUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('下载链接未配置')),
      );
      return;
    }

    // 根据平台执行不同的更新逻辑
    if (Platform.isAndroid) {
      // Android 平台：下载 APK 并安装
      _checkExistingFile();
    } else if (Platform.isIOS) {
      // iOS 平台：跳转到 App Store
      _openAppStore();
    } else {
      // 其他平台：显示不支持提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('当前平台不支持自动更新')),
      );
    }
  }

  void _checkExistingFile() async {
    if (widget.downloadUrl == null) return;

    // 检查是否已有下载的文件
    final physicalFilePath = await ALDownloaderFileManager.getPhysicalFilePathForUrl(widget.downloadUrl!);
    if (physicalFilePath != null) {
      // 有缓存文件，直接设置为下载完成状态
      setState(() {
        _isDownloading = false;
        _isDownloadCompleted = true;
        _downloadProgress = 1.0;
        _downloadedFilePath = physicalFilePath;
      });
      return;
    }

    // 没有缓存文件，开始新下载
    _startActualDownload();
  }

  void _startActualDownload() {
    setState(() {
      _isDownloading = true;
      _isDownloadCompleted = false;  // 重置下载完成状态
      _downloadProgress = 0.0;
      _downloadedFilePath = null;    // 重置文件路径
    });

    // 创建下载信息对象
    _downloadInfo = DownloadInfo(
      name: '应用更新',
      url: widget.downloadUrl!,
      progress: '0',
      downloadStatus: '下载中',
    );

    // 添加下载处理器
    _addDownloadHandler();
    
    // 开始下载
    DownloadProvider().handleDownload(_downloadInfo!);
  }

  void _addDownloadHandler() {
    if (_downloadInfo == null) return;

    _handlerInterfaceId = ALDownloader.addHandlerInterface(
      ALDownloaderHandlerInterface(
        progressHandler: (progress, speed) {
          if (mounted) {
            _downloadInfo!.downloadStatusNotifier.value = '下载中';
            _downloadInfo!.downloadSpeedNotifier.value = _formatSpeed(speed);
            final targetProgress = progress * 100;
            setState(() {
              _downloadProgress = progress.clamp(0.0, 1.0);
            });
            _downloadInfo!.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
          }
        },
        succeededHandler: () async {
          if (mounted) {
            final physicalFilePath = await ALDownloaderFileManager.getPhysicalFilePathForUrl(_downloadInfo!.url);
            if (physicalFilePath != null) {
              setState(() {
                _isDownloading = false;
                _isDownloadCompleted = true;
                _downloadProgress = 1.0;
                _downloadedFilePath = physicalFilePath;
              });
              _downloadInfo!.downloadStatusNotifier.value = '已完成';
              
              // 下载完成后自动安装
              _installApk();
            }
          }
        },
        failedHandler: () {
          if (mounted) {
            setState(() {
              _isDownloading = false;
            });
          }
        },
        pausedHandler: () {
          // 暂停处理（可选）
        },
      ),
      _downloadInfo!.url,
    );
  }

  String _formatSpeed(double speedInBytes) {
    if (speedInBytes < 1024) {
      return "${speedInBytes.toStringAsFixed(1)} B/s";
    } else if (speedInBytes < 1024 * 1024) {
      return "${(speedInBytes / 1024).toStringAsFixed(1)} KB/s";
    } else {
      return "${(speedInBytes / (1024 * 1024)).toStringAsFixed(1)} MB/s";
    }
  }

  void _downloadComplete() {
    // 下载完成后的逻辑
    if (widget.onUpdateNow != null) {
      widget.onUpdateNow!();
    } else {
      // 默认完成逻辑
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('下载完成，正在安装...'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// 跳转到 App Store（iOS 平台）
  void _openAppStore() async {
    if (widget.downloadUrl == null) return;
    
    try {
      final Uri url = Uri.parse(widget.downloadUrl!);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
        // iOS 跳转后关闭弹窗
        // Navigator.of(context).pop();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('无法打开 App Store'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('跳转失败：$e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 安装 APK（Android 平台）
  void _installApk() async {
    if (_downloadedFilePath != null) {
      try {
        // 使用原生方法安装APK
        final channelManager = ChannelManager();
        await channelManager.installApk(_downloadedFilePath!);
        _downloadComplete();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('安装失败：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    // 只有在下载未完成时才清理下载任务，避免删除已下载的文件
    if (_handlerInterfaceId != null) {
      ALDownloader.removeHandlerInterfaceForId(_handlerInterfaceId!);
    }
    super.dispose();
  }

  // 退出应用方法
  void _exitApp() {
    _cleanupIfDownloading();
    Navigator.of(context).pop(false);
    // 延迟退出应用，让弹窗先关闭
    Future.delayed(const Duration(milliseconds: 100), () {
      if (Platform.isAndroid) {
        SystemNavigator.pop();
      } else {
        exit(0);
      }
    });
  }

  // 显示退出确认弹窗
  void _showExitConfirmDialog() {
    CommonAlert.show(
      context: context,
      title: '提示',
      content: '版本过低，需要更新到最新版本才能进入APP',
      barrierDismissible: false,
      showCloseButton: true,
      onClose: () {
        Navigator.of(context).pop(); // 关闭确认弹窗
      },
      cancelText: '关闭APP',
      confirmText: '立刻更新',
      onCancel: () {
        _exitApp(); // 关闭APP
      },
      onConfirm: () {
        // 在确认弹窗中执行更新操作，不关闭强更弹窗
        _startDownload(); // 直接执行更新操作
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // 禁止通过返回键关闭弹窗
      child: Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final double screenWidth = MediaQuery.of(context).size.width;
            final double screenHeight = MediaQuery.of(context).size.height;
            final double dialogWidth = math.min(screenWidth * 0.8, 360);
            
            // 计算内容区域的最大宽度
            final double contentMaxWidth = dialogWidth - 48; // 减去左右padding
            
            // 计算文本高度
            final double textHeight = _calculateTextHeight(
              widget.message ?? '当前版本已不再支持,请立即更新以继续使用',
              contentMaxWidth,
              const TextStyle(fontSize: 16, color: Colors.black),
            );
            
            // 计算所需的最小高度
            final double minContentHeight = 200; // 标题 + 版本号 + 按钮 + padding
            final double requiredContentHeight = textHeight + minContentHeight;
            
            // 动态计算弹窗高度
            final double maxDialogHeight = screenHeight * 0.8;
            final double baseDialogHeight = dialogWidth * 1.25;
            final double calculatedHeight = math.max(baseDialogHeight, requiredContentHeight);
            final double dialogHeight = math.min(calculatedHeight, maxDialogHeight);
            
            final double topSectionHeight = dialogHeight * 0.25; // 顶部四分之一为背景图
            const double extraBottomForClose = 64; // 预留关闭按钮可点区域

            return SizedBox(
              width: dialogWidth,
              height: dialogHeight + extraBottomForClose,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // 白色底卡片（限制在 dialogHeight 内）
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: dialogHeight,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Container(color: Colors.white),
                    ),
                  ),

                  // 顶部四分之一背景图
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: topSectionHeight,
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      child: Image.asset(
                        'assets/images/update_background.png',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),

                  // 顶部强更标识图
                  Positioned(
                    top: -43,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Image.asset(
                        'assets/images/update_enforce.png',
                        width: 126,
                        height: 126,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),

                  // 内容区域（位于顶部背景图下方，且不占用关闭按钮区域）
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: extraBottomForClose,
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(24, topSectionHeight + 12, 24, 24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 标题部分
                          const Text(
                            '发现新版本',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),

                          const SizedBox(height: 4),

                          // 版本号
                          Text(
                            'V${widget.version ?? ''}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.blue,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 12),

                          // 内容部分 - 使用 Expanded 确保内容区域能够正确分配空间
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (!_isDownloading && !_isDownloadCompleted) ...[
                                  // 使用 Flexible 包装内容，允许内容滚动
                                  Flexible(
                                    child: SingleChildScrollView(
                                      child: Text(
                                        widget.message ?? '当前版本已不再支持,请立即更新以继续使用',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.black,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                                ] else if (Platform.isAndroid) ...[
                                  // Android 平台显示下载进度
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        _isDownloadCompleted ? '下载完成' : '正在下载新版本...',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: _isDownloadCompleted ? Colors.green : Colors.black,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 16),
                                      Container(
                                        width: double.infinity,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[300],
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: FractionallySizedBox(
                                          alignment: Alignment.centerLeft,
                                          widthFactor: _downloadProgress.clamp(0.0, 1.0),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: Colors.blue,
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            '${(_downloadProgress.clamp(0.0, 1.0) * 100).toInt()}%',
                                            style: const TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey,
                                            ),
                                          ),
                                          if (_downloadInfo != null)
                                            ValueListenableBuilder<String>(
                                              valueListenable: _downloadInfo!.downloadSpeedNotifier,
                                              builder: (context, speed, child) {
                                                return Text(
                                                  speed,
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.grey,
                                                  ),
                                                );
                                              },
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),

                          const SizedBox(height: 20),

                          // 按钮部分
                          if (!_isDownloading && !_isDownloadCompleted) ...[
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _startDownload,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                                child: Text(
                                  Platform.isIOS ? '前往 App Store' : '立即更新',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ] else if (Platform.isAndroid && _isDownloading && !_isDownloadCompleted) ...[
                            // Android 平台正在下载时，不在弹窗内展示退出按钮，保留外部叉号
                            const SizedBox.shrink(),
                          ] else if (Platform.isAndroid && _isDownloadCompleted) ...[
                            // Android 平台下载完成后显示安装按钮
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _installApk,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                                child: const Text(
                                  '立即安装',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),

                  // 弹窗外部下方关闭叉号按钮（视觉外部，但命中区域在对话框内）
                  Positioned(
                    bottom: 8,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: GestureDetector(
                        onTap: () {
                          _cleanupIfDownloading();
                          final bool shouldExit = widget.exitAppOnClose ?? true;
                          if (shouldExit) {
                            _showExitConfirmDialog();
                          } else {
                            Navigator.of(context).pop();
                          }
                        },
                        child: Image.asset(
                          'assets/images/update_close.png',
                          width: 36,
                          height: 36,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

// 显示版本更新弹窗的便捷方法
class VersionUpdateHelper {
  static void showVersionUpdateDialog(
      BuildContext context, {
        bool exitAppOnClose = true,
        VoidCallback? onUpdateNow,
        String? downloadUrl,  // 添加下载链接参数
        String? message,      // 自定义提示文案
        String? version,
      }) {
    showDialog(
      context: context,
      barrierDismissible: false, // 禁止点击外部关闭
      builder: (BuildContext context) {
        return VersionUpdateDialog(
          exitAppOnClose: exitAppOnClose,
          onUpdateNow: onUpdateNow,
          downloadUrl: downloadUrl,  // 传递下载链接
          message: message,          // 传递提示文案
          version: version,
        );
      },
    );
  }
}