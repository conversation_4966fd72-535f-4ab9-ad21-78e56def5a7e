import 'dart:convert';

import 'package:dlyz_flutter/pages/community/topic_page.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../components/refresh_header.dart';
import '../../components/vote_widget.dart';
import '../../model/emoji.dart';
import '../../net/api/forum_service.dart';
import '../../net/config/http_base_config.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_category.dart';
import '../../utils/log_util.dart';
import '../../components/cache_image.dart';
import '../../components/video_player_card.dart';
import '../../info/forum_info.dart';
import 'community_detail_page.dart';
import 'user_profile_page.dart';

class ForumPostListPage extends StatefulWidget {
  final ForumCategory category;
  final EasyRefreshController easyRefreshController;
  final List<Widget>? headerWidgets;
  final VoidCallback? onRefreshHeader;

  const ForumPostListPage({
    super.key,
    required this.category,
    required this.easyRefreshController,
    this.headerWidgets,
    this.onRefreshHeader,
  });

  @override
  State<ForumPostListPage> createState() => _ForumPostListPageState();
}

class _ForumPostListPageState extends State<ForumPostListPage>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  String? _error;
  final ForumService _forumService = ForumService();
  final ScrollController _scrollController = ScrollController();

  // Pagination state
  final List<ForumPost> _posts = [];
  int _currentPage = 1;
  int _totalPage = 1;
  bool _isLoadingMore = false;
  bool _hasMore = true;
  
  // 关注状态管理
  final Set<int> _followingUserIds = <int>{};
  final Set<int> _followLoadingUserIds = <int>{};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 使用 WidgetsBinding.instance.addPostFrameCallback 确保在组件完全初始化后再加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadPostList(reset: true);
      }
    });

    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    if (!_hasMore || _isLoadingMore || _isLoading) return;
    if (!_scrollController.hasClients) return;
    final position = _scrollController.position;
    if (position.extentAfter < 300) {
      _loadPostList();
    }
  }

  Future<void> _loadPostList({bool reset = false}) async {
    if (!mounted) return;

    LogUtil.d('开始加载帖子列表', tag: 'ForumPostList');

    if (reset) {
      setState(() {
        _isLoading = true;
        _error = null;
        _currentPage = 1;
        _hasMore = true;
      });
    } else {
      if (!_hasMore) return;
      setState(() {
        _isLoadingMore = true;
      });
    }

    try {
      final response = await _forumService.getPostList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        categoryId: widget.category.categoryId,
        categoryName: widget.category.name, // 传入分类名称，用于设置scope参数
        page: reset ? 1 : (_currentPage + 1),
        pageSize: 10,
        stype: widget.category.stype,
        context: context,
      );

      if (!mounted) return;

      LogUtil.d('接口响应: code=${response.code}, message=${response.message}', tag: 'ForumPostList');

      if (response.code == 0 && response.data != null) {
        final data = response.data!;
        setState(() {
          if (reset) {
            _posts
              ..clear()
              ..addAll(data.pageData);
            // 重置时初始化关注状态
            _followingUserIds.clear();
            for (final post in data.pageData) {
              if (post.user.follow == 1) {
                _followingUserIds.add(post.user.userId);
              }
            }
          } else {
            _posts.addAll(data.pageData);
            // 加载更多时添加新的关注状态
            for (final post in data.pageData) {
              if (post.user.follow == 1) {
                _followingUserIds.add(post.user.userId);
              }
            }
          }
          _currentPage = data.currentPage;
          _totalPage = data.totalPage;
          _hasMore = _currentPage < _totalPage && data.pageData.isNotEmpty;
          _isLoading = false;
          _isLoadingMore = false;
        });
        LogUtil.d('帖子列表加载成功', tag: 'ForumPostList');
      } else {
        setState(() {
          _error = response.message ?? '获取帖子列表失败';
          _isLoading = false;
          _isLoadingMore = false;
        });
        LogUtil.w('帖子列表加载失败: ${response.message}', tag: 'ForumPostList');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
        _isLoadingMore = false;
      });
      LogUtil.e('帖子列表加载异常', error: e, stackTrace: stackTrace, tag: 'ForumPostList');
    }
  }

  /// 刷新指定帖子的详情数据
  Future<void> _refreshPostDetail(int threadId, int postIndex) async {
    try {
      final response = await _forumService.getPostDetail(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: threadId,
        context: context,
      );

      if (!mounted) return;

      if (response.code == 0 && response.data != null) {
        setState(() {
          // 更新列表中对应帖子的数据
          if (postIndex < _posts.length) {
            _posts[postIndex] = response.data!;
          }
        });
        LogUtil.d('帖子详情刷新成功', tag: 'ForumPostList');
      } else {
        LogUtil.w('帖子详情刷新失败: ${response.message}', tag: 'ForumPostList');
      }
    } catch (e, stackTrace) {
      LogUtil.e('帖子详情刷新异常', error: e, stackTrace: stackTrace, tag: 'ForumPostList');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 64),
      child: EasyRefresh(
          controller: widget.easyRefreshController,
          header: const CustomerHeader(),
          onRefresh: () async {
            // 通知父页面刷新 headerWidgets
            widget.onRefreshHeader?.call();
            await _loadPostList(reset: true);
            widget.easyRefreshController.finishRefresh();
            widget.easyRefreshController.resetFooter();
          },
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            controller: _scrollController,
            slivers: [
              // 头部组件区域
              if ((widget.headerWidgets ?? const []).isNotEmpty)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(children: widget.headerWidgets!),
                  ),
                ),

              // 各种状态占位：加载中/错误/空
              if (_isLoading)
                const SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: CircularProgressIndicator()),
                )
              else if (_error != null)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: _buildErrorWidget(),
                )
              else if (_posts.isEmpty)
                // 只有在没有头部组件内容时才显示空状态提示
                if ((widget.headerWidgets ?? const []).isEmpty)
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: _buildEmptyWidget(),
                  )
                else
                  // 有头部组件但没有帖子时，不显示任何额外内容
                  const SliverToBoxAdapter(child: SizedBox.shrink())
              else ...[
                // 帖子列表
                SliverPadding(
                  padding: EdgeInsets.zero,
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final post = _posts[index];
                        return _buildPostItem(post, index);
                      },
                      childCount: _posts.length,
                    ),
                  ),
                ),
                // 加载更多/没有更多 提示
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Center(
                      child: _isLoadingMore
                          ? const Padding(
                              padding: EdgeInsets.symmetric(vertical: 12),
                              child: SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                            )
                          : (_hasMore
                              ? const SizedBox.shrink()
                              : Text(
                                  '没有更多了',
                                  style: TextStyle(color: Colors.grey[500], fontSize: 12),
                                )),
                    ),
                  ),
                ),
              ],
            ],
          ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadPostList,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无${widget.category.name}内容',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostItem(ForumPost post, int index) {
    // 判断是否需要显示顶部分割线
    // 如果是第一个item且没有header组件，则不显示分割线
    final bool showTopDivider = !(index == 0 && (widget.headerWidgets?.isEmpty ?? true));
    
    return Column(
      children: [
        // 灰色分割线 - 根据条件显示
        if (showTopDivider)
          Container(
            height: 8,
            color: Colors.grey[200],
            width: double.infinity,
          ),
        GestureDetector(
          onTap: () => _navigateToPostDetail(post),
          child: Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部用户信息区域
                _buildUserHeader(post),

                const SizedBox(height: 12),

                // 帖子标题
                if (post.title.isNotEmpty)
                  _buildTextWithEmojis(
                    post.title,
                    textStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                const SizedBox(height: 8),

                // 帖子内容预览
                _buildContentPreview(post),

                const SizedBox(height: 12),

                // 根据是否有投票模块决定话题标签的位置
                if (post.voteId == null) _buildTopicTags(post),

                // 投票模块（根据vote_id判断是否显示）
                if (post.voteId != null) 
                  VoteWidget(
                    post: post,
                    displayType: VoteDisplayType.list,
                    onVoteSuccess: (updatedPost) async {
                      // 投票成功后重新获取该帖子的详情来刷新投票数据
                      await _refreshPostDetail(post.threadId, index);
                    },
                  ),

                // 如果有投票模块，话题标签显示在投票模块之后，并添加间距
                if (post.voteId != null) ...[
                  const SizedBox(height: 12),
                  _buildTopicTags(post),
                ],

                // 底部信息栏上方增加间距
                SizedBox(height:12),

                // 底部信息栏
                _buildBottomInfo(post),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserHeader(ForumPost post) {
    return Row(
      children: [
        // 用户头像
        GestureDetector(
          onTap: () => _navigateToUserProfile(post.user),
          child: Stack(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey[200],
                backgroundImage: post.user.avatar.isNotEmpty 
                    ? NetworkImage(post.user.avatar) 
                    : null,
                child: post.user.avatar.isEmpty 
                    ? Icon(
                        Icons.person,
                        size: 20,
                        color: Colors.grey[600],
                      )
                    : null,
              ),
              // Badge角标（根据接口返回的badge字段显示）
              if (post.user.badge.isNotEmpty)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: post.user.badge,
                        width: 14,
                        height: 14,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[300],
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.error,
                            size: 8,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名和奖章
              Row(
                children: [
                  Flexible(
                    child: Text(
                      post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // 奖章图标
                  if (_parseMedals(post.user.medal).isNotEmpty) ...[
                    const SizedBox(width: 6),
                    _buildMedalIcons(_parseMedals(post.user.medal)),
                  ],
                ],
              ),
              
              const SizedBox(height: 2),
              
              // 用户标签（根据接口的label和color字段显示）
              if (post.user.label.isNotEmpty)
                Text(
                  post.user.label,
                  style: TextStyle(
                    fontSize: 10,
                    color: _parseColor(post.user.color),
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        
        // 关注按钮 - 只有未关注且不在关注中时才显示
        if (!_followingUserIds.contains(post.user.userId) && !_followLoadingUserIds.contains(post.user.userId))
          GestureDetector(
            onTap: () => _onFollowTap(post.user),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFF4571FB), width: 1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '+关注',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF4571FB),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        // 关注中状态
        if (_followLoadingUserIds.contains(post.user.userId))
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[400]!, width: 1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMediaContent(ForumPost post) {
    // 优先显示视频，如果没有视频则显示图片
    if (_hasVideo(post)) {
      final indexes = post.content.indexes;
      String? videoUrl;
      String? coverUrl;
      if (indexes.containsKey('108')) {
        final videoIndex = indexes['108'];
        if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
          final bodyList = videoIndex['body'] as List;
          if (bodyList.isNotEmpty && bodyList.first is Map<String, dynamic>) {
            final videoData = bodyList.first as Map<String, dynamic>;
            videoUrl = videoData['url'];
            if (videoData['cover'] is Map<String, dynamic>) {
              coverUrl = videoData['cover']['url'];
            } else {
              coverUrl = videoData['thumbUrl'];
            }
          }
        }
      }
      if (videoUrl != null && videoUrl.isNotEmpty) {
        return GestureDetector(
          onTap: () => _playVideoFullscreen(videoUrl!),
          child: _buildVideoThumbnailFromIndex(post),
        );
      }
      return _buildVideoThumbnailFromIndex(post);
    } else if (post.content.images.isNotEmpty) {
      return _buildImageGrid(post.content.images, post.content.images.length > 2 ? 2 : post.content.images.length);
    }
    return const SizedBox.shrink();
  }

  Widget _buildVideoThumbnail(dynamic video) {
    String? videoUrl;
    if (video is Map<String, dynamic>) {
      videoUrl = video['url'];
    } else if (video is String) {
      videoUrl = video;
    }
    
    return GestureDetector(
      onTap: () {
        if (videoUrl != null && videoUrl.isNotEmpty) {
          _playVideoFullscreen(videoUrl!);
        }
      },
      child: Container(
        width: double.infinity,
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 视频缩略图
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.videocam,
                size: 48,
                color: Colors.grey[600],
              ),
            ),
            
            // 播放按钮
            Container(
              width: 60,
              height: 60,
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 30,
              ),
            ),
            
            // 时长显示
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '02:30',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 全屏播放视频
  void _playVideoFullscreen(String videoUrl) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerFullScreenPage(videoUrl: videoUrl),
      ),
    );
  }

  Widget _buildTopicTags(ForumPost post) {
    // 如果没有话题标签，返回空Widget
    if (post.topics.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    List<Map<String, String>> topicList = _parseTopics(post.topics);

    if (topicList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: topicList.map((topicData) {
            return GestureDetector(
              onTap: () => _navigateToTopicPage(topicData['id'] ?? topicData['content'] ?? ''),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '#${topicData['content'] ?? topicData['id'] ?? ''}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  List<Map<String, String>> _parseTopics(String topicsStr) {
    List<Map<String, String>> result = [];

    try {
      // 尝试解析为JSON
      dynamic parsed = json.decode(topicsStr);

      if (parsed is List) {
        // JSON数组格式: [{"id": "1", "content": "游戏"}, {"id": "2", "content": "攻略"}]
        for (var item in parsed) {
          if (item is Map<String, dynamic>) {
            result.add({
              'id': item['id']?.toString() ?? '',
              'content': item['content']?.toString() ?? '',
            });
          }
        }
      } else if (parsed is Map<String, dynamic>) {
        // 单个JSON对象格式: {"id": "1", "content": "游戏"}
        result.add({
          'id': parsed['id']?.toString() ?? '',
          'content': parsed['content']?.toString() ?? '',
        });
      }
    } catch (e) {
      // JSON解析失败，尝试作为简单字符串处理
      List<String> simpleTopics = topicsStr
          .split(RegExp(r'[,，\s]+'))
          .where((topic) => topic.trim().isNotEmpty)
          .map((topic) => topic.trim())
          .toList();

      for (String topic in simpleTopics) {
        result.add({
          'id': topic,
          'content': topic,
        });
      }
    }

    return result;
  }

  void _navigateToTopicPage(String topicIdStr) {
    final int? topicId = int.tryParse(topicIdStr);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicPage(
          topicName: topicIdStr,
          topicId: topicId,
          description: null,
          avatarUrl: null,
          viewCount: 0,
          likeCount: 0,
          threadCount: 0,
        ),
      ),
    );
  }

  Widget _buildBottomInfo(ForumPost post) {
    return Row(
      children: [
        // 发布时间
        Text(
          _formatTimeAgo(post.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
        
        const Spacer(),
        
        // 浏览量
        Icon(
          Icons.visibility_outlined,
          size: 16,
          color: Colors.grey[500],
        ),
        const SizedBox(width: 4),
        Text(
          _formatCount(post.viewCount),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  Widget _buildContentPreview(ForumPost post) {
    final String cleanText = _stripHtmlTags(post.content.text);
    final bool needsTruncation = cleanText.length > 102;
    final String displayText = needsTruncation 
        ? '${cleanText.substring(0, 102)}...' 
        : cleanText;

    // 获取所有图片（包括content.images和图文分离的图片）
    List<dynamic> allImages = _getAllImages(post);
    
    // 检查是否有视频 - 通过索引108判断
    bool hasVideo = _hasVideo(post);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 显示文本内容
        if (displayText.isNotEmpty)
          _buildTextWithEmojis(
            displayText,
            textStyle: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        
        // 显示全文按钮（只在文本被截断时显示）
        if (needsTruncation) ...[
          const SizedBox(height: 8),
          Text(
            '全文',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
        
        // 优先显示视频，如果没有视频则显示图片
        if (hasVideo) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              // 获取视频URL并直接全屏播放
              final indexes = post.content.indexes;
              if (indexes.containsKey('108')) {
                final videoIndex = indexes['108'];
                if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
                  final bodyList = videoIndex['body'] as List;
                  if (bodyList.isNotEmpty && bodyList.first is Map<String, dynamic>) {
                    final videoData = bodyList.first as Map<String, dynamic>;
                    final String? videoUrl = videoData['url'];
                    if (videoUrl != null && videoUrl.isNotEmpty) {
                      _playVideoFullscreen(videoUrl!);
                      return;
                    }
                  }
                }
              }
              // 如果获取不到视频URL，则跳转到详情页
              _navigateToPostDetail(post);
            },
            child: _buildVideoThumbnailFromIndex(post),
          ),
        ] else if (allImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _showImageGallery(context, allImages, 0, post: post),
            child: _buildContentImages(allImages, post: post),
          ),
        ],
      ],
    );
  }

  /// 获取帖子中所有图片（包括content.images和图文分离的图片）
  List<dynamic> _getAllImages(ForumPost post) {
    List<dynamic> allImages = [];
    
    // 先添加content.images中的图片
    if (post.content.images.isNotEmpty) {
      allImages.addAll(post.content.images);
    }
    
    // 判断是否是图文分离的帖子
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    
    if (isSeparatedContent) {
      // 从indexes[101]获取图文分离的图片
      final indexes = post.content.indexes;
      
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                allImages.add(item);
              }
            }
          }
        }
      }
    }
    
    return allImages;
  }

  Widget _buildContentImages(List<dynamic> images, {ForumPost? post}) {
    if (images.isEmpty) return const SizedBox.shrink();
    
    // 最多显示2张图片
    final int displayCount = images.length > 2 ? 2 : images.length;
    
    if (displayCount == 1) {
      // 单张图片
      return _buildSingleImage(images[0], allImages: images, post: post);
    } else {
      // 多张图片网格
      return _buildImageGrid(images, displayCount, post: post);
    }
  }

  Widget _buildSingleImage(dynamic imageData, {List<dynamic>? allImages, ForumPost? post}) {
    String? imageUrl;
    
    // 尝试从不同字段获取图片URL
    if (imageData is Map<String, dynamic>) {
      imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      imageUrl = imageData;
    }
    
    if (imageUrl == null || imageUrl.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImage(
          imageUrl: imageUrl,
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          onTap: () => _showImageGallery(context, allImages ?? [imageData], 0, post: post),
        ),
      ),
    );
  }

  Widget _buildImageGrid(List<dynamic> images, int displayCount, {ForumPost? post}) {
    return SizedBox(
      height: 120,
      child: Row(
        children: List.generate(displayCount, (index) {
          final imageData = images[index];
          String? imageUrl;
          
          if (imageData is Map<String, dynamic>) {
            imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
          } else if (imageData is String) {
            imageUrl = imageData;
          }
          
          return Expanded(
            child: GestureDetector(
              onTap: () => _showImageGallery(context, images, index, post: post),
              child: Container(
                margin: EdgeInsets.only(right: index < (displayCount - 1) ? 4 : 0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: imageUrl != null && imageUrl.isNotEmpty
                          ? CachedImage(
                              imageUrl: imageUrl,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image,
                                color: Colors.grey[600],
                              ),
                            ),
                    ),
                    
                    // 如果是最后一张图片且还有更多图片，显示"+N"标识
                    if (index == (displayCount - 1) && images.length > displayCount)
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: Colors.black.withValues(alpha: 0.5),
                        ),
                        child: Center(
                          child: Text(
                            '+${images.length - displayCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else {
        // 超过1天直接显示日期时间
        return '${postTime.year}-${postTime.month.toString().padLeft(2, '0')}-${postTime.day.toString().padLeft(2, '0')} ${postTime.hour.toString().padLeft(2, '0')}:${postTime.minute.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      return '刚刚';
    }
  }

  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }

  /// 构建带表情包的文本Widget
  Widget _buildTextWithEmojis(String text, {TextStyle? textStyle, int? maxLines, TextOverflow? overflow}) {
    final emojis = ForumInfo().emojis;
    
    // 临时调试日志 - 检查初始化状态
    if (text.contains(':kelian:')) {
      print('=== 表情包调试 ===');
      print('文本内容: $text');
      print('表情包列表长度: ${emojis.length}');
      print('ForumInfo是否已初始化: ${ForumInfo().isInitialized}');
      if (emojis.isNotEmpty) {
        print('前3个表情包:');
        for (var i = 0; i < 3 && i < emojis.length; i++) {
          print('  ${emojis[i].code} -> ${emojis[i].url}');
        }
      }
    }
    
    if (emojis.isEmpty || !text.contains(':')) {
      return Text(text, style: textStyle, maxLines: maxLines, overflow: overflow);
    }

    final List<InlineSpan> spans = [];
    final regex = RegExp(r':([^:]+):');
    int currentIndex = 0;
    
    for (final match in regex.allMatches(text)) {
      // 添加表情前的文本
      if (match.start > currentIndex) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, match.start),
          style: textStyle,
        ));
      }
      
      // 查找表情包 - 直接遍历emojis列表查找匹配的code
      final emojiCode = match.group(1);
      final fullEmojiCode = ":${emojiCode}:";
      Emoji? foundEmoji;
      
      print('正在查找表情包: $fullEmojiCode');

      
      for (final emoji in emojis) {
        if (emoji.code == fullEmojiCode) {
          foundEmoji = emoji;
          print('找到匹配的表情包: ${emoji.code} -> ${emoji.url}');
          break;
        }
      }
      
      if (foundEmoji != null && foundEmoji.url.isNotEmpty) {
        // 添加表情包图片
        spans.add(WidgetSpan(
          alignment: PlaceholderAlignment.middle,
          child: Container(
            width: 18,
            height: 18,
            child: CachedNetworkImage(
              imageUrl: foundEmoji.url,
              width: 18,
              height: 18,
              fit: BoxFit.contain,
              placeholder: (context, url) => Text(':$emojiCode:', style: textStyle),
              errorWidget: (context, url, error) {
                print('表情包加载失败: $url, 错误: $error');
                return Text(':$emojiCode:', style: textStyle);
              },
            ),
          ),
        ));
      } else {
        print('未找到表情包: $fullEmojiCode');
        // 如果找不到表情包，保持原文本
        spans.add(TextSpan(
          text: ':$emojiCode:',
          style: textStyle,
        ));
      }
      
      currentIndex = match.end;
    }
    
    // 添加剩余文本
    if (currentIndex < text.length) {
      spans.add(TextSpan(
        text: text.substring(currentIndex),
        style: textStyle,
      ));
    }
    
    return Text.rich(
      TextSpan(children: spans),
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  String _stripHtmlTags(String html) {
    if (html.isEmpty) return '';
    
    // 移除HTML标签
    String result = html.replaceAll(RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false), '');
    
    // 替换HTML实体
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"');
    
    // 移除多余的空白字符和换行
    result = result
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return result;
  }

  /// 跳转到帖子详情页
  void _navigateToPostDetail(ForumPost post) async {
    final int postIndex = _posts.indexWhere((p) => p.threadId == post.threadId);
    
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommunityDetailPage(post: post),
      ),
    );
    
    // 如果从详情页返回且可能发生了投票，刷新该帖子的数据
    if (result == true || result == null) {
      // result为null表示正常返回，result为true表示明确需要刷新
      if (postIndex != -1) {
        await _refreshPostDetail(post.threadId, postIndex);
      }
    }
  }

  /// 跳转到用户个人资料页面
  void _navigateToUserProfile(ForumUser user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfilePage(user: user),
      ),
    );
  }

  /// 关注用户
  Future<void> _onFollowTap(ForumUser user) async {
    // 如果正在关注中，直接返回
    if (_followLoadingUserIds.contains(user.userId)) {
      return;
    }
    
    // 如果已经关注，直接返回
    if (_followingUserIds.contains(user.userId)) {
      return;
    }
    
    // 添加到关注中状态
    setState(() {
      _followLoadingUserIds.add(user.userId);
    });
    
    try {
      final response = await _forumService.followUser(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        toUserId: user.userId,
        context: context,
      );
      
      if (!mounted) return;
      
      if (response.code == 0) {
        // 关注成功，添加到已关注列表
        setState(() {
          _followingUserIds.add(user.userId);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已关注 ${user.nickname}'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // 关注失败
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message ?? '关注失败'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('关注失败: $e'),
          duration: const Duration(seconds: 2),
        ),
      );
    } finally {
      // 移除关注中状态
      if (mounted) {
        setState(() {
          _followLoadingUserIds.remove(user.userId);
        });
      }
    }
  }

  /// 显示图片画廊（支持左右滑动）
  void _showImageGallery(BuildContext context, List<dynamic> images, int initialIndex, {ForumPost? post}) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return _ImageGalleryDialog(
          images: images, 
          initialIndex: initialIndex, 
          post: post,
          onViewPost: post != null ? () {
            Navigator.of(context).pop(); // 关闭图片浏览
            _navigateToPostDetail(post); // 跳转到帖子详情
          } : null,
        );
      },
    );
  }

  /// 检查帖子是否有视频 - 通过索引108判断
  bool _hasVideo(ForumPost post) {
    final indexes = post.content.indexes;
    if (indexes.containsKey('108')) {
      final videoIndex = indexes['108'];
      if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
        final bodyList = videoIndex['body'] as List;
        return bodyList.isNotEmpty;
      }
    }
    return false;
  }

  /// 从索引108构建视频缩略图
  Widget _buildVideoThumbnailFromIndex(ForumPost post) {
    final indexes = post.content.indexes;
    if (!indexes.containsKey('108')) {
      return const SizedBox.shrink();
    }

    final videoIndex = indexes['108'];
    if (videoIndex is! Map<String, dynamic> || videoIndex['body'] is! List) {
      return const SizedBox.shrink();
    }

    final bodyList = videoIndex['body'] as List;
    if (bodyList.isEmpty) {
      return const SizedBox.shrink();
    }

    final videoData = bodyList.first;
    if (videoData is! Map<String, dynamic>) {
      return const SizedBox.shrink();
    }

    final String? thumbUrl = videoData['thumbUrl'];
    
    // 从cover字段获取视频预览图
    String? coverUrl;
    if (videoData['cover'] is Map<String, dynamic>) {
      coverUrl = videoData['cover']['url'];
    }
    
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 视频缩略图
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: (coverUrl != null && coverUrl.isNotEmpty)
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedImage(
                      imageUrl: coverUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  )
                : (thumbUrl != null && thumbUrl.isNotEmpty)
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CachedImage(
                          imageUrl: thumbUrl,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Icon(
                        Icons.videocam,
                        size: 48,
                        color: Colors.grey[600],
                      ),
          ),
          
          // 播放按钮
          Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          ),
          
          // 时长显示
          Positioned(
            bottom: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                '02:30',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 解析奖章字符串并返回有效的URL列表
  List<String> _parseMedals(dynamic medal) {
    if (medal == null) return [];
    
    List<String> medalUrls = [];
    
    try {
      if (medal is String) {
        if (medal.trim().isEmpty) return [];
        
        // 尝试解析为JSON数组
        try {
          final dynamic parsed = json.decode(medal);
          if (parsed is List) {
            for (var item in parsed) {
              if (item is String && _isValidUrl(item)) {
                medalUrls.add(item);
              }
            }
          }
        } catch (e) {
          // 如果不是JSON，检查是否是单个URL
          if (_isValidUrl(medal)) {
            medalUrls.add(medal);
          }
        }
      } else if (medal is List) {
        for (var item in medal) {
          if (item is String && _isValidUrl(item)) {
            medalUrls.add(item);
          }
        }
      }
    } catch (e) {
      // 解析失败，返回空列表
      return [];
    }
    
    return medalUrls;
  }
  
  /// 验证URL格式是否正确
  bool _isValidUrl(String url) {
    if (url.trim().isEmpty) return false;
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// 构建奖章图标
  Widget _buildMedalIcons(List<String> medalUrls) {
    if (medalUrls.isEmpty) return const SizedBox.shrink();
    
    return Wrap(
      spacing: 4,
      children: medalUrls.take(3).map((url) { // 最多显示3个奖章
        return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey[300]!, width: 0.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: CachedNetworkImage(
              imageUrl: url,
              width: 20,
              height: 20,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.emoji_events,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[200],
                child: Icon(
                  Icons.error,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 解析颜色字符串（支持#RRGGBB格式）
  Color _parseColor(String colorString) {
    if (colorString.isEmpty) {
      return Colors.blue; // 默认颜色
    }
    
    try {
      // 移除可能存在的#号
      String cleanColor = colorString.replaceAll('#', '');
      
      // 确保是6位十六进制
      if (cleanColor.length == 6) {
        return Color(int.parse('FF$cleanColor', radix: 16));
      }
      
      // 如果是8位，包含透明度
      if (cleanColor.length == 8) {
        return Color(int.parse(cleanColor, radix: 16));
      }
      
      return Colors.blue; // 解析失败时的默认颜色
    } catch (e) {
      return Colors.blue; // 解析失败时的默认颜色
    }
  }

}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;
  final ForumPost? post;
  final VoidCallback? onViewPost;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
    this.post,
    this.onViewPost,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
            // 查看帖子按钮
            if (widget.post != null && widget.onViewPost != null)
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: Center(
                  child: GestureDetector(
                    onTap: widget.onViewPost,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            '查看帖子',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white,
                            size: 16,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}