import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/game_circle_config_provider.dart';
import '../../components/cache_image.dart';
import '../settings/SettingsPage.dart';
import '../settings/AccountManagementPage.dart';
import '../settings/AddressManagementPage.dart';
import '../../webview/web_router.dart';
import '../../services/profile_api_service.dart';
import '../../model/game_circle.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  String? _lastUserId; // 记录上次的用户ID
  List<GameCircle> _gameCircles = []; // 游戏圈子列表
  bool _isLoadingCircles = false; // 圈子加载状态

  @override
  void initState() {
    super.initState();
    // 页面加载时获取用户详细信息
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (userProvider.isLoggedIn) {
        _lastUserId = userProvider.currentUserId;
        userProvider.fetchUserDetailInfo();
        setState(() {
          _isLoadingCircles = true;
        });
        _loadQuanzi();
      }
    });
  }

  void _checkUserChange(UserProvider userProvider) {
    final currentUserId = userProvider.currentUserId;
    
    // 检查用户是否发生变化
    if (_lastUserId != currentUserId) {
      _lastUserId = currentUserId;
      
      // 用户变更时重新获取用户详细信息
      if (userProvider.isLoggedIn && currentUserId != null) {
        userProvider.fetchUserDetailInfo();
        _loadQuanzi(); // 重新加载圈子数据
      }
    }
  }

  void _loadQuanzi() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn || userProvider.currentTicket == null) {
      setState(() {
        _gameCircles = [];
        _isLoadingCircles = false;
      });
      return;
    }

    if (!mounted) {
       _isLoadingCircles = false;
      return;
    }

    try {
      final api = ProfileApiService();
      final response = await api.getPlayCircle(userProvider.currentTicket!);
      
      if (response.success && response.data != null) {
        // 处理返回的GameCircleListResponse数据
        setState(() {
          if (response.data is GameCircleListResponse) {
            _gameCircles = (response.data as GameCircleListResponse).circles;
          } else {
            _gameCircles = [];
          }
          _isLoadingCircles = false;
        });
      } else {
        setState(() {
          _gameCircles = [];
          _isLoadingCircles = false;
        });
      }
    } catch (e) {
      debugPrint('加载游戏圈子失败: $e');
      if (!mounted) return;
      setState(() {
        _gameCircles = [];
        _isLoadingCircles = false;
      });
    }
  }

  // 下拉刷新方法
  Future<void> _onRefresh() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) {
      return;
    }

    try {
      // 同时刷新用户详细信息和游戏圈子
      await Future.wait([
        userProvider.fetchUserDetailInfo(),
        _refreshQuanzi(),
      ]);
    } catch (e) {
      debugPrint('刷新失败: $e');
    }
  }

  // 刷新圈子数据（不显示加载状态，用于下拉刷新）
  Future<void> _refreshQuanzi() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn || userProvider.currentTicket == null) {
      return;
    }

    try {
      final api = ProfileApiService();
      final response = await api.getPlayCircle(userProvider.currentTicket!);
      
      if (!mounted) return;
      
      if (response.success && response.data != null) {
        setState(() {
          if (response.data is GameCircleListResponse) {
            _gameCircles = (response.data as GameCircleListResponse).circles;
          } else {
            _gameCircles = [];
          }
          _isLoadingCircles = false;
        });
      } else {
        setState(() {
          _gameCircles = [];
          _isLoadingCircles = false;
        });
      }
    } catch (e) {
      debugPrint('刷新游戏圈子失败: $e');
      if (!mounted) return;
      setState(() {
        _gameCircles = [];
        _isLoadingCircles = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<UserProvider, GameCircleConfigProvider>(
      builder: (context, userProvider, configProvider, child) {
        // // 检查用户是否发生变化，如果是则重新获取用户详细信息
        _checkUserChange(userProvider);
        
        final user = userProvider.currentUser;
        final isLoggedIn = userProvider.isLoggedIn;
        final backgroundImage = configProvider.backgroundImage;

        return Scaffold(
          backgroundColor: const Color.fromRGBO(246, 247, 249, 1),
          body: Stack(
            children: [
              // 固定的游戏背景图 - 在最底层，不会移动
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 450,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: backgroundImage != null
                          ? NetworkImage(backgroundImage) as ImageProvider
                          : const AssetImage('assets/images/gray_main_bg.png'),
                      fit: BoxFit.cover,
                      alignment: Alignment.topCenter,
                    ),
                  ),
                ),
              ),

              // Transform.translate(
              //   offset: const Offset(0, 100),
              //   child:
              // ),
              // 可滚动的内容 - 在顶层，可以向上滑动
              RefreshIndicator(
                onRefresh: _onRefresh,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(), // 确保可以触发下拉刷新
                  child: Column(
                    children: [
                      // 顶部留白空间，显示背景图和为头像预留空间
                      const SizedBox(height: 100),
                      
                        // 用户信息区域
                      Transform.translate(
                        offset: const Offset(0, 75), // 向上移动40px，使容器顶部对齐头像中间
                        child: Stack(
                          children: [
                            // 白色用户信息容器
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 0),
                              padding: const EdgeInsets.only(left: 30, right: 16, top: 50, bottom: 36),
                              decoration: BoxDecoration(
                                color: const Color.fromRGBO(246, 247, 249, 1),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 10, offset: const Offset(0, 5)),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        isLoggedIn && user?.userDetail.alias != null ? '${user!.userDetail.alias}' : '斗罗宇宙公民',
                                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black87),
                                      ),
                                      const SizedBox(width: 5),
                                      if (isLoggedIn && user?.userDetail.vipLevelNum != null)
                                        Image.asset(
                                            'assets/images/vip_level_${user?.userDetail.vipLevelNum}.png',
                                            width: 60,
                                            height: 30,
                                            fit: BoxFit.contain,
                                            errorBuilder: (context, error, stackTrace) {
                                              return SizedBox();
                                            },
                                          ),
                                        ],
                                  ),
                                  
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      const Text('UID: ', style: TextStyle(fontSize: 14, color: Colors.grey)),
                                      Text(
                                        isLoggedIn && user?.muid != null ? user!.muid! : '未登录',
                                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                                      ),
                                      const SizedBox(width: 20),
                                      const Text('属地: ', style: TextStyle(fontSize: 14, color: Colors.grey)),
                                      Text(
                                        isLoggedIn && user?.userDetail.area != null ? user!.userDetail.area! : '未知',
                                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // 用户头像（重叠在白色容器上）
                      Transform.translate(
                        offset: const Offset(-140, -105),
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: Colors.white, width: 2)),
                          child: ClipOval(
                            child:
                                isLoggedIn && user?.userDetail.avatar != null && user!.userDetail.avatar!.isNotEmpty
                                    ? CachedImage(
                                      imageUrl: user.userDetail.avatar!,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                      errorWidget: Image.asset('assets/images/avatar.png', fit: BoxFit.cover),
                                    )
                                    : Image.asset('assets/images/avatar.png', fit: BoxFit.cover),
                          ),
                        ),
                      ),

                      // const SizedBox(height: 20),
                      Transform.translate(
                        offset: const Offset(0, -25), // 向下移动20px
                        child: Container(
                          color: const Color.fromRGBO(246, 247, 249, 1),
                          child: Column(
                            children: [
                              const SizedBox(height: 0),
                              // 我的游戏圈子
                              Container(
                                margin: const EdgeInsets.symmetric(horizontal: 16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.1),
                                      spreadRadius: 1,
                                      blurRadius: 5,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 16),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 15),
                                      child: const Text(
                                        '我的游戏圈子',
                                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black87),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 15, bottom: 5),
                                      child: SizedBox(
                                        height: 120,
                                        child: _isLoadingCircles
                                            ? const Center(
                                                child: CircularProgressIndicator(),
                                              )
                                            : _gameCircles.isEmpty
                                                ? const Center(
                                                    child: Text(
                                                      '暂无游戏圈子',
                                                      style: TextStyle(
                                                        fontSize: 14,
                                                        color: Colors.grey,
                                                      ),
                                                    ),
                                                  )
                                                : ListView.builder(
                                                    scrollDirection: Axis.horizontal,
                                                    itemCount: _gameCircles.length,
                                                    itemBuilder: (context, index) {
                                                      return _buildGameItem(_gameCircles[index]);
                                                    },
                                                  ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 20),

                              // 菜单项
                              Container(
                                margin: const EdgeInsets.symmetric(horizontal: 16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.1),
                                      spreadRadius: 1,
                                      blurRadius: 5,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  children: [
                                    _buildMenuItem('assets/images/account_manage_icon.png', '账号管理', () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(builder: (context) => const AccountManagementPage()),
                                      );
                                    }),
                                    // 根据绑定状态显示手机相关菜单
                                    ..._buildPhoneMenuItems(isLoggedIn, user),
                                    // _buildDivider(),
                                    _buildMenuItem('assets/images/address_manage_icon.png', '地址管理', () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => const AddressManagementPage(),
                                        ),
                                      );
                                    }),
                                    // _buildDivider(),
                                    _buildMenuItem('assets/images/advice_icon.png', '建议与反馈', () {
                                      _openWebView("https://user.37.com.cn/sdkv1/service/home", '建议与反馈');
                                    }),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 10),
                            ],
                          ),
                        ),
                      ),
                      // 白色背景区域开始 - 确保后续内容有白色背景
                      // const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),

              // 设置图标 - 固定在右上角，在最顶层确保可点击
              Positioned(
                top: MediaQuery.of(context).padding.top + 35, // 考虑状态栏高度
                right: 30,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => const SettingsPage()));
                    },
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      width: 44, // 增大点击区域
                      height: 44, // 增大点击区域
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20)
                      ),
                      child: Image.asset('assets/images/setting.png', width: 30, height: 30, fit: BoxFit.contain),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 游戏项构建方法
  Widget _buildGameItem(GameCircle circle) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      width: 100, // 固定宽度以便文本换行
      child: Column(
        children: [
          const SizedBox(height: 15), // 添加15px的顶部间距
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[200], // 默认背景色
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: circle.circleIcon.isNotEmpty
                  ? CachedImage(
                      imageUrl: circle.circleIcon,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorWidget: Image.asset(
                        'assets/images/game_icon.png',
                        fit: BoxFit.cover,
                      ),
                    )
                  : Image.asset(
                      'assets/images/game_icon.png',
                      fit: BoxFit.cover,
                    ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            circle.displayName,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // 菜单项构建方法
  Widget _buildMenuItem(String imageString, String title, VoidCallback onTap, {String? phoneNumber}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            Image.asset(imageString, width: 24, height: 24, fit: BoxFit.contain),
            const SizedBox(width: 12),
            Expanded(child: Text(title, style: const TextStyle(fontSize: 16, color: Colors.black87))),
            if (phoneNumber != null && phoneNumber.isNotEmpty) ...[
              Text(
                phoneNumber,
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(width: 8),
            ],
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  // 根据绑定状态构建手机相关菜单项
  List<Widget> _buildPhoneMenuItems(bool isLoggedIn, dynamic user) {
    if (!isLoggedIn || user?.userDetail == null) {
      return [];
    }

    final hasLoginPhone = user.userDetail.loginPhone != null && user.userDetail.loginPhone!.isNotEmpty;
    final hasMibaoPhone = user.userDetail.mibaoPhone != null && user.userDetail.mibaoPhone!.isNotEmpty;

    List<Widget> menuItems = [];

    if (hasLoginPhone) {
      // 情况1：已绑定登录手机，只显示绑定登录手机入口（修改）
      menuItems.add(
        _buildMenuItem(
          'assets/images/phone_icon.png',
          '绑定登录手机',
          () => _openWebView('https://user.37.com.cn/sdkv1/user-system/changeloginphone', '修改登录手机'),
          phoneNumber: user.userDetail.loginPhone,
        ),
      );
    } else {
      // 情况2和3：未绑定登录手机，显示两个入口
      menuItems.add(
        _buildMenuItem(
          'assets/images/phone_icon.png',
          '绑定登录手机',
          () => _openWebView('https://user.37.com.cn/sdkv1/user-system/bindloginphone', '绑定登录手机'),
        ),
      );

      menuItems.add(
        _buildMenuItem(
          'assets/images/password_icon.png',
          '绑定密保手机',
          () => _openWebView('https://37.com.cn/user-system/encrypt/index', '绑定密保手机'),
          phoneNumber: hasMibaoPhone ? user.userDetail.mibaoPhone : null,
        ),
      );
    }

    return menuItems;
  }

  // 打开WebView的通用方法
  Future<void> _openWebView(String url, String title) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn || userProvider.currentTicket == null) {
        return;
      }

      final api = ProfileApiService();
      final response = await api.ticket2Token(userProvider.currentTicket!);
      
      String? token;
      if (response.success && response.data != null) {
        token = response.data!.token;
      }

      if (!mounted) return; // 检查widget是否还mounted

      final params = <String, dynamic>{};
      if (token != null && token.isNotEmpty) {
        params['token'] = token;
      }

      WebRouter.jumpToWebPage(context, url, title: title, params: params);
    } catch (e) {
      debugPrint('获取token失败: $e');
      if (!mounted) return;
      
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('网络错误，请稍后重试'),
          duration: Duration(seconds: 2),
        ),
      );
      
    }
  }
}
