import 'dart:convert';
import 'package:dlyz_flutter/webview/handler/CloseHandler.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../webview/js_bridge.dart';
import '../webview/webview_wrapper.dart';

class RiskWebPage {
  /// 显示风险验证弹窗
  static Future<bool?> showRiskDialog(BuildContext context, String riskJson) {
    final map = jsonDecode(riskJson) as Map<String, dynamic>;
    final url = map['url'] as String?;
    
    if (url == null || url.isEmpty) {
      return Future.value(false);
    }

    // 解析 URL 回退开关。...?retry=1
    final uri = Uri.parse(url);
    final isNeedRetryFromUrl = uri.queryParameters['retry'] == '1';

    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (dialogContext) {
        return _RiskWebViewDialog(
          url: url,
          isNeedRetryFromUrl: isNeedRetryFromUrl,
        );
      },
    );
  }
}

/// 专门用于风险验证的 WebViewDialog
class _RiskWebViewDialog extends StatefulWidget {
  final String url;
  final bool isNeedRetryFromUrl;

  const _RiskWebViewDialog({
    required this.url,
    required this.isNeedRetryFromUrl,
  });

  @override
  State<_RiskWebViewDialog> createState() => _RiskWebViewDialogState();
}

class _RiskWebViewDialogState extends State<_RiskWebViewDialog> {
  late WebViewWrapperController _webViewController;
  late JSBridge _jsBridge;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewWrapperController();
    _jsBridge = JSBridge(context);
    _jsBridge.registerHandler(CloseHandler(context, _webViewController, handlerFunc: (message) {
      Navigator.of(context).pop(widget.isNeedRetryFromUrl);
    }));

  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (!didPop) {
          // 页面关闭时的统一判定
          final bool needRetry = widget.isNeedRetryFromUrl;
          Navigator.of(context).pop(needRetry);
        }
      },
      child: Material(
        type: MaterialType.transparency,
        child: WebViewWrapper(
          controller: _webViewController,
          initialUrl: widget.url,
          jsBridge: _jsBridge,
          backgroundColor: Colors.transparent,
          onNavigationRequest: (request) async {
            return NavigationDecision.navigate;
          },
        ),
      ),
    );
  }
}
