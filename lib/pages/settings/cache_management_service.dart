import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../../utils/log_util.dart';

class CacheUtil {
  static Future<int> total() async {
    Directory tempDir = await getTemporaryDirectory();
    int total = await _reduce(tempDir);
    return total;
  }

  static Future<void> clear() async {
    Directory tempDir = await getTemporaryDirectory();
    await _delete(tempDir);
  }

  static Future<int> _reduce(final FileSystemEntity file) async {
    if (file is File) {
      int length = await file.length();
      return length;
    }

    if (file is Directory) {
      final List<FileSystemEntity> children = file.listSync();

      int total = 0;

      if (children.isNotEmpty) {
        for (final FileSystemEntity child in children) {
          total += await _reduce(child);
        }
      }

      return total;
    }

    return 0;
  }

  static Future<void> _delete(FileSystemEntity file) async {
    if (file is Directory) {
      final List<FileSystemEntity> children = file.listSync();
      for (final FileSystemEntity child in children) {
        await _delete(child);
      }
    } else {
      await file.delete();
    }
  }
}

class CacheManagementService {
  /// 计算缓存大小
  static Future<String> calculateCacheSize() async {
    try {
      LogUtil.d('开始计算缓存大小...');
      
      int totalSize = await CacheUtil.total();
      
      // 格式化大小显示
      final formattedSize = _formatBytes(totalSize);
      LogUtil.d('缓存大小计算完成: $formattedSize ($totalSize bytes)');
      
      return formattedSize;
    } catch (e) {
      LogUtil.e('计算缓存大小失败: $e');
      return '0 MB';
    }
  }
  
  /// 格式化字节大小
  static String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }
  
  /// 清理缓存
  static Future<bool> clearCache() async {
    try {
      LogUtil.d('开始清理缓存...');
      
      await CacheUtil.clear();
      
      LogUtil.d('缓存清理完成');
      return true;
    } catch (e) {
      LogUtil.e('清理缓存失败: $e');
      return false;
    }
  }
  
  /// 显示清理缓存确认弹窗
  static Future<void> showClearCacheDialog(BuildContext context, String cacheSize) async {
    final shouldClear = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('清理缓存'),
          content: Text('确定要清理缓存吗'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFF999999)),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text(
                '确定',
                style: TextStyle(color: Color(0xFF007AFF)),
              ),
            ),
          ],
        );
      },
    );
    
    if (shouldClear == true && context.mounted) {
      await _performCacheClearing(context);
    }
  }
  
  /// 执行缓存清理
  static Future<void> _performCacheClearing(BuildContext context) async {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('正在清理缓存...'),
            ],
          ),
        );
      },
    );
    
    try {
      final success = await clearCache();
      
      if (context.mounted) {
        // 关闭加载对话框
        Navigator.pop(context);
        
        // 显示结果
        final message = success ? '缓存清理成功' : '缓存清理完成，部分文件可能无法删除';
        final color = success ? Colors.green : Colors.orange;
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: color,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        // 关闭加载对话框
        Navigator.pop(context);
        
        // 显示错误
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('缓存清理失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}