import 'package:flutter/material.dart';
import '../../manager/network_manager.dart';

class PlaySettingsPage extends StatefulWidget {
  const PlaySettingsPage({super.key});

  @override
  State<PlaySettingsPage> createState() => _PlaySettingsPageState();
}

class _PlaySettingsPageState extends State<PlaySettingsPage> {
  NetworkAccessControl _selectedVideoAutoPlay = NetworkAccessControl.allowAll;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// 加载设置
  void _loadSettings() {
    setState(() {
      _selectedVideoAutoPlay = NetworkManager().getNetworkAccessControl();
    });
  }

  /// 保存设置
  void _saveSettings() {
    NetworkManager().setNetworkAccessControl(_selectedVideoAutoPlay);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '播放设置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Container(
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    '视频和动图自动播放',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF666666),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                _buildRadioOption(
                  title: '移动网络和Wi-Fi网络',
                  value: NetworkAccessControl.allowAll,
                  groupValue: _selectedVideoAutoPlay,
                  onChanged: (value) {
                    setState(() {
                      _selectedVideoAutoPlay = value!;
                    });
                    _saveSettings();
                  },
                ),
                _buildRadioOption(
                  title: '仅Wi-Fi网络',
                  value: NetworkAccessControl.wifiOnly,
                  groupValue: _selectedVideoAutoPlay,
                  onChanged: (value) {
                    setState(() {
                      _selectedVideoAutoPlay = value!;
                    });
                    _saveSettings();
                  },
                ),
                _buildRadioOption(
                  title: '关闭',
                  value: NetworkAccessControl.disabled,
                  groupValue: _selectedVideoAutoPlay,
                  onChanged: (value) {
                    setState(() {
                      _selectedVideoAutoPlay = value!;
                    });
                    _saveSettings();
                  },
                ),
              ],
            ),
          ),
          ],
      ),
    );
  }

  Widget _buildRadioOption({
    required String title,
    required NetworkAccessControl value,
    required NetworkAccessControl groupValue,
    required ValueChanged<NetworkAccessControl?> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            SizedBox(
              width: 20,
              height: 20,
              child: value == groupValue
                  ? Image.asset(
                      'assets/images/checked_icon.png',
                      width: 20,
                      height: 20,
                      errorBuilder: (context, error, stackTrace) {
                        // 如果图片加载失败，使用默认的选中图标
                        return Container(
                          width: 20,
                          height: 20,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color(0xFF007AFF),
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 14,
                          ),
                        );
                      },
                    )
                  : Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: const Color(0xFFCCCCCC),
                          width: 2,
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

}