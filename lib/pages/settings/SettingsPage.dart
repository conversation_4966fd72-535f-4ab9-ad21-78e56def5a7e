import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../webview/webview_dialog.dart';
import '../../services/app_route_manager.dart';
import '../demo/DemoPage.dart';
import 'AboutUsPage.dart';
import 'update_check_service.dart';
import 'cache_management_service.dart';
import 'PlaySettingsPage.dart';
import 'push_notification_settings_page.dart';
import '../../webview/web_router.dart';
import '../../providers/user_provider.dart';
import '../../services/profile_api_service.dart';
import 'package:provider/provider.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _cacheSize = '计算中...';

  @override
  void initState() {
    super.initState();
    _calculateCacheSize();
  }

  /// 计算缓存大小
  Future<void> _calculateCacheSize() async {
    final size = await CacheManagementService.calculateCacheSize();
    if (mounted) {
      setState(() {
        _cacheSize = size;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '设置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildSettingItem(context, '播放设置'),
                  _buildSettingItem(context, '推送设置'),
                  _buildSettingItemWithValue('清理缓存', _cacheSize, onTap: () async {
                    await CacheManagementService.showClearCacheDialog(context, _cacheSize);
                    // 清理完成后重新计算缓存大小
                    _calculateCacheSize();
                  }),
                  _buildSettingItem(context, '检查更新'),
                  _buildSettingItem(context, '关于我们'),
                  _buildSettingItem(context, '个人信息收集清单'),
                  _buildSettingItem(context, '第三方共享与个人信息清单'),
                  if (kDebugMode) ...[
                    _buildSettingItem(context, 'Demo 功能'),
                    _buildDivider(),
                  ],
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
          _buildLogoutButton(),
          const SizedBox(height: 34),
        ],
      ),
    );
  }

  Widget _buildSettingItem(BuildContext context, String title) {
    return GestureDetector(
      onTap: () {
        switch (title) {
          case '检查更新':
            UpdateCheckService.checkForUpdate(context, showLatestVersionToast: true);
            break;
          case '关于我们':
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AboutUsPage(),
              ),
            );
            break;
          case '播放设置':
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PlaySettingsPage(),
              ),
            );
            break;
          case '推送设置':
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PushNotificationSettingsPage(),
              ),
            );
            break;
          case '个人信息收集清单':
            _openWebView('https://user.37.com.cn/sdkv1/user-system/shareInfoList?adaptNotchScreen=1&forceOrientation=1', '个人信息收集清单');
            // WebRouter.jumpToWebPage(context, 'https://user.37.com.cn/sdkv1/user-system/shareInfoList?adaptNotchScreen=1&forceOrientation=1', title: '个人信息收集清单', params: {});
            break;
          case '第三方共享与个人信息清单':
            _openWebView('https://user.37.com.cn/sdkv1/user-system/shareInfoList?adaptNotchScreen=1&forceOrientation=1', '个人信息收集清单');
            // WebRouter.jumpToWebPage(context, 'https://user.37.com.cn/sdkv1/user-system/shareInfoList?adaptNotchScreen=1&forceOrientation=1', title: '', params: {});
            break;
          case '建议与反馈':
            Navigator.of(context).push(
              PageRouteBuilder(
                opaque: false,
                barrierDismissible: true,
                barrierColor: Colors.transparent,
                pageBuilder: (context, animation, secondaryAnimation) {
                  return WebViewDialog(
                    url: "https://user.37.com.cn/sdkv1/service/home",
                    title: '官网',
                    showToolBar: true,
                  );
                },
              ),
            );
            break;
          case 'Demo 功能':
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DemoPage(title: 'Demo'),
              ),
            );
            break;
        }
      },
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFFCCCCCC),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItemWithValue(String title, String value, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            Row(
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF999999),
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Color(0xFFCCCCCC),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

Future<void> _openWebView(String url, String title) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn || userProvider.currentTicket == null) {
        return;
      }

      final api = ProfileApiService();
      final response = await api.ticket2Token(userProvider.currentTicket!);
      
      String? token;
      if (response.success && response.data != null) {
        token = response.data!.token;
      }

      if (!mounted) return; // 检查widget是否还mounted

      final params = <String, dynamic>{};
      if (token != null && token.isNotEmpty) {
        params['token'] = token;
      }

      WebRouter.jumpToWebPage(context, url, title: title, params: params);
    } catch (e) {
      debugPrint('获取token失败: $e');
      if (!mounted) return;
      
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('网络错误，请稍后重试'),
          duration: Duration(seconds: 2),
        ),
      );
      
    }
  }

  Widget _buildDivider() {
    return Container(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.only(left: 16),
        height: 1,
        color: const Color(0xFFF0F0F0),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: Builder(
        builder: (context) => TextButton(
          onPressed: () async {
            // 显示确认对话框
            final shouldLogout = await _showLogoutConfirmDialog(context);
            if (shouldLogout == true) {
              // 使用统一的退出登录方法
              AppRouteManager.navigateAfterLogout(context);
            }
          },
          style: TextButton.styleFrom(
            backgroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text(
            '退出登录',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF007AFF),
            ),
          ),
        ),
      ),
    );
  }

  Future<bool?> _showLogoutConfirmDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出当前账号吗？'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text(
              '取消',
              style: TextStyle(color: Color(0xFF999999)),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              '确定',
              style: TextStyle(color: Color(0xFF007AFF)),
            ),
          ),
        ],
      ),
    );
  }
}