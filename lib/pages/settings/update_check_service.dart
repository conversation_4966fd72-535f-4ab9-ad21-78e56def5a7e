import 'package:flutter/material.dart';
import '../../net/api/force_update_service.dart';
import '../update/update_dialog.dart';
import '../../utils/log_util.dart';

class UpdateCheckService {
  // 更新类型常量
  static const String TYPE_NORMAL = "1"; // 正常，不需要更新
  static const String TYPE_UPDATE = "2"; // 普通更新
  static const String TYPE_FORCE = "3"; // 强制更新

  /// 检查是否需要强更
  /// [context] 上下文
  /// [showLatestVersionToast] 是否在检测到最新版本时显示提示，默认为false
  static Future<void> checkForUpdate(
    BuildContext context, {
    bool showLatestVersionToast = false,
  }) async {
    try {
      LogUtil.d('开始检查强更...');
      final response = await ForceUpdateService.checkForceUpdate(context: context);
      if (response.isSuccess && response.data != null) {
        LogUtil.d('强更接口调用成功，检查更新配置');
        await _checkUpdateConfig(context, response.data!, showLatestVersionToast);
      } else {
        LogUtil.d('强更接口调用失败: ${response.message}');
      }
    } catch (e) {
      LogUtil.e('检查强更失败: $e');
    }
  }

  /// 检查更新配置
  static Future<void> _checkUpdateConfig(
    BuildContext context, 
    dynamic data, 
    bool showLatestVersionToast,
  ) async {
    try {
      // 检查是否需要强制更新
      if (data.needForceUpdate && data.forceUpdateData != null) {
        final updateData = data.forceUpdateData!;
        final updateType = updateData.utype;
        final apkUrl = updateData.uurl;
        final updateContent = updateData.uct;
        final version = updateData.uvs;
        LogUtil.d('检查更新配置: updateType=$updateType, apkUrl=$apkUrl, version=$version');
        if (updateType == TYPE_NORMAL) {
          // 正常，不需要更新
          LogUtil.d('当前版本正常，无需更新');
        } else if (updateType == TYPE_UPDATE) {
          // 普通更新
          LogUtil.d('发现可用更新，显示更新弹窗');
          if (context.mounted) {
            _showUpdateDialog(
              context,
              false,
              updateContent,
              apkUrl,
              version,
            );
          }
        } else if (updateType == TYPE_FORCE) {
          // 强制更新
          LogUtil.d('发现强制更新，显示强制更新弹窗');
          if (context.mounted) {
            _showUpdateDialog(
              context,
              true,
              updateContent,
              apkUrl,
              version,
            );
          }
        } else {
          // 未知更新类型，当作无需更新处理
          LogUtil.d('未知更新类型: $updateType，当作无需更新处理');
        }
      } else {
        // 不需要强制更新
        LogUtil.d('不需要强制更新');
      }
    } catch (e) {
      LogUtil.e('检查更新配置失败: $e');
    }
  }

  /// 显示更新弹窗
  static void _showUpdateDialog(
    BuildContext context,
    bool isForce,
    String updateContent,
    String apkUrl,
    String version,
  ) {
    LogUtil.d('准备显示更新弹窗: isForce=$isForce, apkUrl=$apkUrl, version=$version');
    LogUtil.d('更新内容: $updateContent');

    try {
      // 使用 VersionUpdateDialog 组件
      VersionUpdateHelper.showVersionUpdateDialog(
        context,
        exitAppOnClose: isForce, // 强制更新时关闭弹窗会退出应用
        downloadUrl: apkUrl,
        message:
            updateContent.isNotEmpty ? updateContent : '发现新版本，建议立即更新以获得更好的体验。',
        version: version,
      );

      LogUtil.d('更新弹窗显示调用完成');
    } catch (e) {
      LogUtil.e('显示更新弹窗失败: $e');
    }
  }
}