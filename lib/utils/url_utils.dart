/// URL工具类
/// 提供URL参数添加和处理功能
class UrlUtils {
  
  /// 向URL添加单个参数
  /// 
  /// [url] 原始URL
  /// [key] 参数名
  /// [value] 参数值
  /// 
  /// 返回添加参数后的URL
  static String addParameter(String url, String key, dynamic value) {
    if (url.isEmpty || key.isEmpty) {
      return url;
    }
    
    final String paramString = '$key=${Uri.encodeComponent(value.toString())}';
    
    // 检查URL是否包含查询参数
    if (url.contains('?')) {
      // 检查?后是否有数据
      final int questionIndex = url.indexOf('?');
      final String queryPart = url.substring(questionIndex + 1);
      
      if (queryPart.isEmpty) {
        // ?后没有数据，直接添加参数
        return '$url$paramString';
      } else {
        // ?后有数据，使用&连接
        return '$url&$paramString';
      }
    } else {
      // 没有?，添加?和参数
      return '$url?$paramString';
    }
  }
  
  /// 向URL添加多个参数
  /// 
  /// [url] 原始URL
  /// [parameters] 参数Map，key为参数名，value为参数值
  /// 
  /// 返回添加所有参数后的URL
  static String addParameters(String url, Map<String, dynamic> parameters) {
    if (url.isEmpty || parameters.isEmpty) {
      return url;
    }
    
    String resultUrl = url;
    
    for (final entry in parameters.entries) {
      if (entry.key.isNotEmpty && entry.value != null) {
        resultUrl = addParameter(resultUrl, entry.key, entry.value);
      }
    }
    
    return resultUrl;
  }
  
  /// 向URL添加多个参数（链式调用风格）
  /// 
  /// [url] 原始URL
  /// 
  /// 返回UrlBuilder实例，支持链式调用
  static UrlBuilder builder(String url) {
    return UrlBuilder(url);
  }
  
  /// 解析URL中的查询参数
  /// 
  /// [url] 要解析的URL
  /// 
  /// 返回参数Map
  static Map<String, String> parseParameters(String url) {
    final Map<String, String> parameters = {};
    
    if (url.isEmpty || !url.contains('?')) {
      return parameters;
    }
    
    final int questionIndex = url.indexOf('?');
    final String queryPart = url.substring(questionIndex + 1);
    
    if (queryPart.isEmpty) {
      return parameters;
    }
    
    final List<String> pairs = queryPart.split('&');
    
    for (final String pair in pairs) {
      if (pair.isNotEmpty) {
        final List<String> keyValue = pair.split('=');
        if (keyValue.length == 2) {
          final String key = Uri.decodeComponent(keyValue[0]);
          final String value = Uri.decodeComponent(keyValue[1]);
          parameters[key] = value;
        }
      }
    }
    
    return parameters;
  }
  
  /// 移除URL中的指定参数
  /// 
  /// [url] 原始URL
  /// [parameterName] 要移除的参数名
  /// 
  /// 返回移除参数后的URL
  static String removeParameter(String url, String parameterName) {
    if (url.isEmpty || parameterName.isEmpty || !url.contains('?')) {
      return url;
    }
    
    final int questionIndex = url.indexOf('?');
    final String basePart = url.substring(0, questionIndex);
    final String queryPart = url.substring(questionIndex + 1);
    
    if (queryPart.isEmpty) {
      return url;
    }
    
    final List<String> pairs = queryPart.split('&');
    final List<String> filteredPairs = [];
    
    for (final String pair in pairs) {
      if (pair.isNotEmpty && !pair.startsWith('$parameterName=')) {
        filteredPairs.add(pair);
      }
    }
    
    if (filteredPairs.isEmpty) {
      return basePart;
    } else {
      return '$basePart?${filteredPairs.join('&')}';
    }
  }
  
  /// 获取URL的基础部分（不包含查询参数）
  /// 
  /// [url] 原始URL
  /// 
  /// 返回基础URL
  static String getBaseUrl(String url) {
    if (url.isEmpty || !url.contains('?')) {
      return url;
    }
    
    final int questionIndex = url.indexOf('?');
    return url.substring(0, questionIndex);
  }
  
  /// 获取URL的查询字符串部分
  /// 
  /// [url] 原始URL
  /// 
  /// 返回查询字符串（不包含?）
  static String getQueryString(String url) {
    if (url.isEmpty || !url.contains('?')) {
      return '';
    }
    
    final int questionIndex = url.indexOf('?');
    return url.substring(questionIndex + 1);
  }
  
  /// 检查URL是否包含指定参数
  /// 
  /// [url] 要检查的URL
  /// [parameterName] 参数名
  /// 
  /// 返回是否包含该参数
  static bool hasParameter(String url, String parameterName) {
    final Map<String, String> parameters = parseParameters(url);
    return parameters.containsKey(parameterName);
  }
  
  /// 获取URL中指定参数的值
  /// 
  /// [url] 要解析的URL
  /// [parameterName] 参数名
  /// 
  /// 返回参数值，如果不存在则返回null
  static String? getParameterValue(String url, String parameterName) {
    final Map<String, String> parameters = parseParameters(url);
    return parameters[parameterName];
  }
}

/// URL构建器类，支持链式调用
class UrlBuilder {
  String _url;
  
  UrlBuilder(this._url);
  
  /// 添加参数
  /// 
  /// [key] 参数名
  /// [value] 参数值
  /// 
  /// 返回UrlBuilder实例，支持链式调用
  UrlBuilder addParam(String key, dynamic value) {
    _url = UrlUtils.addParameter(_url, key, value);
    return this;
  }
  
  /// 添加多个参数
  /// 
  /// [parameters] 参数Map
  /// 
  /// 返回UrlBuilder实例，支持链式调用
  UrlBuilder addParams(Map<String, dynamic> parameters) {
    _url = UrlUtils.addParameters(_url, parameters);
    return this;
  }
  
  /// 移除参数
  /// 
  /// [parameterName] 要移除的参数名
  /// 
  /// 返回UrlBuilder实例，支持链式调用
  UrlBuilder removeParam(String parameterName) {
    _url = UrlUtils.removeParameter(_url, parameterName);
    return this;
  }
  
  /// 构建最终的URL
  /// 
  /// 返回构建完成的URL字符串
  String build() {
    return _url;
  }
  
  /// 获取当前URL（同build方法）
  /// 
  /// 返回当前URL字符串
  String get url => _url;
  
  @override
  String toString() {
    return _url;
  }
}