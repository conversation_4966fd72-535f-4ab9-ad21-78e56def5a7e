import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../model/game_role.dart';
import '../model/user.dart';
import '../providers/game_circle_provider.dart';
import '../providers/user_provider.dart';
import '../services/bind_account_service.dart';
import '../utils/log_util.dart';

/// 角色管理Provider
/// 负责管理用户角色的绑定、查询、切换等操作
class RoleProvider with ChangeNotifier {
  // 私有属性
  List<GameRoleV2> _boundRoles = [];
  List<GameRole> _allRoles = [];
  GameRoleV2? _selectedRole;
  bool _isLoading = false;
  String? _error;

  // 服务实例
  final BindAccountService _bindAccountService = BindAccountService();

  // 公开的getter
  List<GameRoleV2> get boundRoles => _boundRoles;
  List<GameRole> get allRoles => _allRoles;
  GameRoleV2? get selectedRole => _selectedRole;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasBoundRoles => _boundRoles.isNotEmpty;
  bool get hasSelectedRole => _selectedRole != null;

  /// 从所有角色列表中匹配等级信息
  String _findLevelFromAllRoles(int drid, String drname) {
    try {
      // 先按照drid匹配
      final matchedRole = _allRoles.where((role) => role.drid == drid.toString()).firstOrNull;
      if (matchedRole != null) {
        return matchedRole.drlevel;
      }
      
      // 如果drid匹配失败，尝试按照角色名称匹配
      final nameMatchedRole = _allRoles.where((role) => role.drname == drname).firstOrNull;
      if (nameMatchedRole != null) {
        return nameMatchedRole.drlevel;
      }
      
      // 都匹配失败，返回空字符串
      return '';
    } catch (e) {
      LogUtil.w('匹配角色等级失败: $e', tag: 'RoleProvider');
      return '';
    }
  }

  /// 更新boundRoles的等级信息
  List<GameRoleV2> _updateBoundRolesWithLevel(List<GameRoleV2> boundRoles) {
    return boundRoles.map((boundRole) {
      final level = _findLevelFromAllRoles(boundRole.drid, boundRole.drname);
      return boundRole.copyWith(drlevel: level);
    }).toList();
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 设置错误信息
  void setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// 清除错误信息
  void clearError() {
    setError(null);
  }

  /// 清除所有数据
  void clearData() {
    _boundRoles = [];
    _allRoles = [];
    _selectedRole = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  /// 获取用户已绑定的角色列表
  Future<List<GameRoleV2>?> getBoundUserRoles(
    BuildContext context, {
    User? user,
  }) async {
    try {
      // 获取当前游戏圈子的tgid
      String tgid = '37'; // 默认值
      try {
        final gameCircleProvider = Provider.of<GameCircleProvider>(
          context,
          listen: false,
        );
        if (gameCircleProvider.hasSelectedGameCircle &&
            gameCircleProvider.selectedGameCircle != null) {
          tgid = gameCircleProvider.selectedGameCircle!.tgid;
        }
      } catch (e) {
        LogUtil.w('获取当前游戏圈子tgid失败，使用默认值: $e', tag: 'RoleProvider');
      }

      LogUtil.d('开始加载已绑定角色列表，tgid: $tgid', tag: 'RoleProvider');

      // 调用角色列表V2接口
      final response = await _bindAccountService.getRoleListV2(tgid: tgid);

      if (response.success &&
          response.data != null &&
          response.data!.data != null) {
        final roles = response.data!.data!.list;
        LogUtil.d('已绑定角色列表加载成功，获取到 ${roles.length} 个角色',
            tag: 'RoleProvider');
        return roles;
      } else {
        LogUtil.w('已绑定角色列表加载失败: ${response.message}', tag: 'RoleProvider');
        return [];
      }
    } catch (e) {
      LogUtil.e('获取已绑定角色列表异常: $e', tag: 'RoleProvider');
      return null;
    }
  }

  /// 获取用户所有角色列表
  Future<List<GameRole>?> getAllUserRoles(
    BuildContext context,
    String uid,
  ) async {
    try {
      // 获取当前游戏圈子的tgid
      String tgid = '37'; // 默认值
      try {
        final gameCircleProvider = Provider.of<GameCircleProvider>(
          context,
          listen: false,
        );
        if (gameCircleProvider.hasSelectedGameCircle &&
            gameCircleProvider.selectedGameCircle != null) {
          tgid = gameCircleProvider.selectedGameCircle!.tgid;
        }
      } catch (e) {
        LogUtil.w('获取当前游戏圈子tgid失败，使用默认值: $e', tag: 'RoleProvider');
      }

      LogUtil.d('开始加载所有角色列表，tgid: $tgid, uid: $uid', tag: 'RoleProvider');

      // 调用角色列表接口（获取所有角色）
      final response = await _bindAccountService.getRoleList(
        tgid: tgid,
        uid: uid,
      );

      if (response.success && response.data != null) {
        LogUtil.d('所有角色列表加载成功，获取到 ${response.data!.length} 个角色',
            tag: 'RoleProvider');
        return response.data!;
      } else {
        LogUtil.w('所有角色列表加载失败: ${response.message}', tag: 'RoleProvider');
        return [];
      }
    } catch (e) {
      LogUtil.e('获取所有角色列表异常: $e', tag: 'RoleProvider');
      return null;
    }
  }

  /// 加载绑定的角色列表
  Future<void> loadBoundRoles(BuildContext context) async {
    try {
      setLoading(true);
      clearError();

      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );

      // 确保有选中的游戏圈子
      if (!gameCircleProvider.hasSelectedGameCircle ||
          gameCircleProvider.selectedGameCircle == null) {
        LogUtil.w('没有选中的游戏圈子，跳过角色加载', tag: 'RoleProvider');
        _boundRoles = [];
        _selectedRole = null;
        setLoading(false);
        return;
      }

      final selectedCircle = gameCircleProvider.selectedGameCircle!;
      final tgid = selectedCircle.tgid;

      LogUtil.d('开始加载角色列表，tgid: $tgid', tag: 'RoleProvider');

      // 首先调用getRoleListV2接口获取绑定角色列表
      final response = await _bindAccountService.getRoleListV2(tgid: tgid);

      if (response.success &&
          response.data != null &&
          response.data!.data != null) {
        var roles = response.data!.data!.list;
        LogUtil.d('绑定角色列表加载成功，获取到 ${roles.length} 个角色', tag: 'RoleProvider');

        // 如果有角色，获取第一个角色的uid来加载所有角色列表（用于匹配等级）
        if (roles.isNotEmpty) {
          final uid = roles.first.uid.toString();
          try {
            final allRolesResponse = await _bindAccountService.getRoleList(
              tgid: tgid,
              uid: uid,
            );
            
            if (allRolesResponse.success && allRolesResponse.data != null) {
              _allRoles = allRolesResponse.data!;
              LogUtil.d('所有角色列表加载成功，获取到 ${_allRoles.length} 个角色', tag: 'RoleProvider');
              
              // 更新绑定角色的等级信息
              roles = _updateBoundRolesWithLevel(roles);
            }
          } catch (e) {
            LogUtil.w('加载所有角色列表失败，跳过等级匹配: $e', tag: 'RoleProvider');
          }
        }

        // 找到默认选择的角色
        GameRoleV2? defaultRole;
        for (final role in roles) {
          if (role.isPicked) {
            defaultRole = role;
            break;
          }
        }

        _boundRoles = roles;
        _selectedRole = defaultRole;
      } else {
        LogUtil.w('角色列表加载失败: ${response.message}', tag: 'RoleProvider');
        _boundRoles = [];
        _selectedRole = null;
      }
    } catch (e) {
      LogUtil.e('加载角色列表异常: $e', tag: 'RoleProvider');
      setError('加载角色列表失败: $e');
      _boundRoles = [];
      _selectedRole = null;
    } finally {
      setLoading(false);
    }
  }

  /// 检查可绑定的角色
  /// 返回可绑定的角色列表，如果查询失败返回null
  Future<List<GameRole>?> checkAvailableRoles(
    BuildContext context,
    String uid,
  ) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;

      if (currentUser == null) {
        LogUtil.w('当前用户为空，无法查询绑定角色', tag: 'RoleProvider');
        return null;
      }

      // 并行查询所有角色和已绑定角色
      final results = await Future.wait([
        getAllUserRoles(context, uid),
        getBoundUserRoles(context, user: currentUser),
      ]);

      final allRoles = results[0] as List<GameRole>?;
      final boundRoles = results[1] as List<GameRoleV2>?;

      // 如果任一查询失败，返回null
      if (allRoles == null || boundRoles == null) {
        return null;
      }

      // 如果没有角色，返回空列表
      if (allRoles.isEmpty) {
        return [];
      }

      // 如果没有已绑定的角色，所有角色都可绑定
      if (boundRoles.isEmpty) {
        return allRoles;
      }

      // 创建已绑定角色的ID集合，用于快速查找
      final boundRoleIds = boundRoles.map((role) => role.drid.toString()).toSet();

      // 过滤出未绑定的角色
      final availableRoles = allRoles.where((role) {
        return !boundRoleIds.contains(role.drid);
      }).toList();

      return availableRoles;
    } catch (e) {
      LogUtil.e('检查可绑定角色异常: $e', tag: 'RoleProvider');
      return null;
    }
  }

  /// 设置默认角色（切换角色）
  Future<bool> setDefaultRole(
    BuildContext context, {
    required String roleFavoriteId,
    required String uid,
    required String rolePid,
    required String roleGid,
    required String drid,
    required String dsid,
    required String drname,
    required String dsname,
  }) async {
    try {
      LogUtil.d('开始切换默认角色: $drname', tag: 'RoleProvider');

      final response = await _bindAccountService.setDefaultRole(
        roleFavoriteId: roleFavoriteId,
        uid: uid,
        rolePid: rolePid,
        roleGid: roleGid,
        drid: drid,
        dsid: dsid,
        drname: drname,
        dsname: dsname,
      );

      if (response.success) {
        LogUtil.d('角色切换成功', tag: 'RoleProvider');

        // 更新本地状态
        _boundRoles = _boundRoles.map((role) {
          return role.copyWith(
            isPicked: role.roleFavoriteId.toString() == roleFavoriteId,
          );
        }).toList();

        // 更新当前选中的角色
        _selectedRole = _boundRoles.firstWhere(
          (role) => role.roleFavoriteId.toString() == roleFavoriteId,
          orElse: () => _selectedRole!,
        );

        notifyListeners();
        return true;
      } else {
        LogUtil.e('角色切换失败: ${response.message}', tag: 'RoleProvider');
        setError('角色切换失败: ${response.message}');
        return false;
      }
    } catch (e) {
      LogUtil.e('角色切换异常: $e', tag: 'RoleProvider');
      setError('角色切换失败: $e');
      return false;
    }
  }

  /// 更新绑定角色列表（外部调用）
  void updateBoundRoles(List<GameRoleV2> roles) {
    // 如果有_allRoles数据，更新等级信息
    if (_allRoles.isNotEmpty) {
      roles = _updateBoundRolesWithLevel(roles);
    }
    
    _boundRoles = roles;
    
    // 更新选中角色
    GameRoleV2? defaultRole;
    for (final role in roles) {
      if (role.isPicked) {
        defaultRole = role;
        break;
      }
    }
    _selectedRole = defaultRole;
    
    notifyListeners();
  }

  /// 更新所有角色列表（外部调用）
  void updateAllRoles(List<GameRole> roles) {
    _allRoles = roles;
    notifyListeners();
  }

  /// 更新选中角色（外部调用）
  void updateSelectedRole(GameRoleV2? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      notifyListeners();
    }
  }
}