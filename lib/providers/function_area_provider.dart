import 'package:flutter/foundation.dart';
import '../model/function_area.dart';
import '../utils/log_util.dart';

/// 功能区数据管理
/// 负责管理功能区列表的状态
class FunctionAreaProvider extends ChangeNotifier {
  List<FunctionArea> _functionAreas = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<FunctionArea> get functionAreas => _functionAreas;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _functionAreas.isNotEmpty;

  /// 更新功能区数据
  void updateFunctionAreas(List<FunctionArea> functionAreas) {
    _functionAreas = functionAreas;
    _error = null;
    _isLoading = false;

    LogUtil.d('功能区数据更新成功: ${_functionAreas.length} 个功能区',
              tag: 'FunctionAreaProvider');
    notifyListeners();
  }

  /// 清除数据
  void clearData() {
    _functionAreas = [];
    _error = null;
    _isLoading = false;

    LogUtil.d('功能区数据已清除', tag: 'FunctionAreaProvider');
    notifyListeners();
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      if (loading) {
        _error = null;
      }
      notifyListeners();
    }
  }

  /// 设置错误状态
  void setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  /// 根据模块名查找功能区
  FunctionArea? findByModule(String areaModule) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaModule == areaModule,
      );
    } catch (e) {
      return null;
    }
  }

  /// 根据areaModule查找功能区
  FunctionArea? findByName(String areaModule) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaModule == areaModule,
      );
    } catch (e) {
      return null;
    }
  }
}

enum FunctionModuleType {
  coupon, //代金券
  direct_pay, //特惠商城
  gift_center, //礼包中心
  coin_mall, //积分商城
}