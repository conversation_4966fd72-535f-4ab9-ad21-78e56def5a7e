/// Token响应数据模型
class TokenResponse {
  /// 访问令牌
  final String? token;

  const TokenResponse({
    this.token,
  });

  /// 从JSON创建对象
  factory TokenResponse.fromJson(Map<String, dynamic> json) {
    return TokenResponse(
      token: json['token'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'token': token,
    };
  }

  @override
  String toString() {
    return 'TokenResponse(token: $token)';
  }
}