
import 'package:dlyz_flutter/model/game_post_list.dart';
import 'download_info.dart';

class GameCenterPocketData {
  final GamePostList androidGameItems;
  final GamePostList iosGameItems;

  GameCenterPocketData()
      : androidGameItems = GamePostList(
          offset: 0,
          record: [
            GameListDetail(
              tGid: 147,
              tGidName: '斗罗大陆：猎魂世界',
              tag: '冒险·动作·MMORPG',
              slogan: '这一次，重新定义斗罗世界！',
              banner: 'https://imgcs.s98s2.com/aicc/video/1752075938385.mp4',
              bannerPreview: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
              detailPage: 'https://lhsj.37.com.cn/',
              icon: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
              floatConfig: GameFloatConfig(floatMsg: true),
              downloadInfo: DownloadInfo(
                name: '斗罗大陆：猎魂世界',
                url: 'https://dlcs.37tgy.com/upload/1_1023086_19077/douluodaluliehunshijie-pinzhuanguanggaosibu37zigengxin2_1001.apk',
                fileName: '',
                directoryPath: '',
              ),
              gameActionConfig: GameActionConfig(
                officialPackageName: 'com.tt.lhgzs.dddllhsj',
                packageInfo: [
                  PackageInfo(
                    id: 1,
                    packageType: PackageType.official,
                    title: '官方包',
                    packageName: 'com.tt.lhgzs.dddllhsj',
                    wechatMiniProgramId: 0,
                    iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
                    downloadUrl: 'https://dlcs.37tgy.com/upload/1_1023086_19077/douluodaluliehunshijie-pinzhuanguanggaosibu37zigengxin2_1001.apk',
                    floatConfig: GameFloatConfig(floatMsg: true),
                    hasInstall: false,
                  )
                ],
              ),
            ),
            GameListDetail(
              tGid: 5839,
              tGidName: '斗罗大陆:魂师对决',
              tag: '卡牌·回合制·养成·多人联机',
              slogan: '真正能打的斗罗大陆',
              banner: 'https://imgcs.s98s2.com/image/webSite/article/1657186238000/%E4%B8%87%E5%8D%83%E9%AD%82%E5%B8%88%E9%87%8D%E8%81%9A-ljy690.jpg',
              bannerPreview: 'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
              detailPage: 'https://detail.page/2',
              icon: 'https://imgcs.s98s2.com/image/webSite/article/1625907799000/logo.png',
              floatConfig: GameFloatConfig(floatMsg: false),
              downloadInfo: DownloadInfo(
                name: '斗罗大陆:魂师对决',
                url: 'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk',
                fileName: '',
                directoryPath: '',
              ),
              gameActionConfig: GameActionConfig(
                officialPackageName: 'com.sy.dldlhsdj.gw',
                packageInfo: [
                  PackageInfo(
                    id: 1,
                    packageType: PackageType.official,
                    title: '官方包',
                    packageName: 'com.sy.dldlhsdj.gw',
                    wechatMiniProgramId: 0,
                    iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
                    downloadUrl: 'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk',
                    floatConfig: GameFloatConfig(floatMsg: false),
                    hasInstall: false,
                  ),
                  PackageInfo(
                    id: 2,
                    packageType: PackageType.wechat,
                    title: '微信小游戏',
                    packageName: '',
                    wechatMiniProgramId: 234,
                    iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
                    downloadUrl: '',
                    floatConfig: GameFloatConfig(floatMsg: false),
                    hasInstall: true,
                  ),
                ],
              ),
            ),
          ],
          floatConfig: GameFloatConfig(floatMsg: false),
        ),
        iosGameItems = GamePostList(
          offset: 0,
          record: [
            GameListDetail(
              tGid: 147,
              tGidName: '斗罗大陆：猎魂世界',
              tag: '冒险·动作·MMORPG',
              slogan: '这一次，重新定义斗罗世界！',
              banner: 'https://imgcs.s98s2.com/aicc/video/1752075938385.mp4',
              bannerPreview: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
              detailPage: 'https://lhsj.37.com.cn/',
              icon: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
              floatConfig: GameFloatConfig(floatMsg: true),
              downloadInfo: DownloadInfo(
                name: '斗罗大陆：猎魂世界',
                url: 'https://apps.apple.com/cn/app/id6739239402',
                fileName: '',
                directoryPath: '',
              ),
              gameActionConfig: GameActionConfig(
                officialPackageName: 'com.tt.lhgzs.dddllhsj',
                packageInfo: [
                  PackageInfo(
                    id: 1,
                    packageType: PackageType.official,
                    title: '官方包',
                    packageName: 'com.tt.lhgzs.dddllhsj',
                    wechatMiniProgramId: 0,
                    iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
                    downloadUrl: 'https://apps.apple.com/cn/app/id6739239402',
                    floatConfig: GameFloatConfig(floatMsg: true),
                    hasInstall: false,
                  )
                ],
              ),
            ),
            GameListDetail(
              tGid: 5839,
              tGidName: '斗罗大陆:魂师对决',
              tag: '卡牌·回合制·养成·多人联机',
              slogan: '真正能打的斗罗大陆',
              banner: 'https://imgcs.s98s2.com/image/webSite/article/1657186238000/%E4%B8%87%E5%8D%83%E9%AD%82%E5%B8%88%E9%87%8D%E8%81%9A-ljy690.jpg',
              bannerPreview: 'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
              detailPage: 'https://detail.page/2',
              icon: 'https://apps.apple.com/cn/app/id6739239402',
              floatConfig: GameFloatConfig(floatMsg: false),
              downloadInfo: DownloadInfo(
                name: '斗罗大陆:魂师对决',
                url: 'https://apps.apple.com/cn/app/id6739239402',
                fileName: '',
                directoryPath: '',
              ),
              gameActionConfig: GameActionConfig(
                officialPackageName: 'com.sy.dldlhsdj.gw',
                packageInfo: [
                  PackageInfo(
                    id: 1,
                    packageType: PackageType.official,
                    title: '官方包',
                    packageName: 'com.sy.dldlhsdj.gw',
                    wechatMiniProgramId: 0,
                    iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
                    downloadUrl: 'https://apps.apple.com/cn/app/id6739239402',
                    floatConfig: GameFloatConfig(floatMsg: false),
                    hasInstall: false,
                  ),
                  PackageInfo(
                    id: 2,
                    packageType: PackageType.wechat,
                    title: '微信小游戏',
                    packageName: '',
                    wechatMiniProgramId: 234,
                    iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
                    downloadUrl: '',
                    floatConfig: GameFloatConfig(floatMsg: false),
                    hasInstall: true,
                  ),
                ],
              ),
            ),
          ],
          floatConfig: GameFloatConfig(floatMsg: false),
        );
}