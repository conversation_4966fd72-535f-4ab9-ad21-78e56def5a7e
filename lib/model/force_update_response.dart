/// 强更接口响应数据模型
/// 只处理API响应中data字段的内容
class ForceUpdateResponse {
  /// 是否需要强制更新
  final bool needForceUpdate;
  
  /// 强制更新数据
  final ForceUpdateData? forceUpdateData;

  ForceUpdateResponse({
    required this.needForceUpdate,
    this.forceUpdateData,
  });

  factory ForceUpdateResponse.fromJson(Map<String, dynamic> json) {
    return ForceUpdateResponse(
      needForceUpdate: json['need_force_update'] ?? false,
      forceUpdateData: json['force_update_data'] != null
          ? ForceUpdateData.fromJson(json['force_update_data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'need_force_update': needForceUpdate,
      'force_update_data': forceUpdateData?.toJson(),
    };
  }

  @override
  String toString() {
    return 'ForceUpdateResponse{needForceUpdate: $needForceUpdate, forceUpdateData: $forceUpdateData}';
  }
}

/// 强制更新数据
class ForceUpdateData {
  /// 更新类型
  final String utype;
  
  /// 更新URL
  final String uurl;
  
  /// 更新内容提示
  final String uct;
  
  /// 版本号
  final String uvs;

  ForceUpdateData({
    required this.utype,
    required this.uurl,
    required this.uct,
    required this.uvs,
  });

  factory ForceUpdateData.fromJson(Map<String, dynamic> json) {
    return ForceUpdateData(
      utype: json['utype'] ?? 0,
      uurl: json['uurl'] ?? '',
      uct: json['uct'] ?? '',
      uvs: json['uvs'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'utype': utype,
      'uurl': uurl,
      'uct': uct,
      'uvs': uvs,
    };
  }

  @override
  String toString() {
    return 'ForceUpdateData{utype: $utype, uurl: $uurl, uct: $uct, uvs: $uvs}';
  }
}