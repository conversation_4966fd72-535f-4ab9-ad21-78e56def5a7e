/// 子用户授权结果响应模型
class AuthRetResponse {
  final int? state;
  final String? msg;
  final AuthRetData? data;

  const AuthRetResponse({
    this.state,
    this.msg,
    this.data,
  });

  factory AuthRetResponse.fromJson(Map<String, dynamic> json) {
    return AuthRetResponse(
      state: json['state'] as int?,
      msg: json['msg'] as String?,
      data: json['data'] != null 
          ? AuthRetData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'state': state,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

/// 子用户授权结果数据模型
class AuthRetData {
  final int uid;
  final String uname;
  final int status;

  const AuthRetData({
    required this.uid,
    required this.uname,
    required this.status,
  });

  factory AuthRetData.fromJson(Map<String, dynamic> json) {
    return AuthRetData(
      uid: json['uid'] as int,
      uname: json['uname'] as String,
      status: json['status'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'uname': uname,
      'status': status,
    };
  }

  /// 状态描述
  String get statusDescription {
    switch (status) {
      case 1:
        return '通过';
      case 2:
        return '未操作';
      case 3:
        return '过期(需要重新操作)';
      default:
        return '未知状态';
    }
  }

  /// 是否授权通过
  bool get isAuthorized => status == 1;

  /// 是否未操作
  bool get isPending => status == 2;

  /// 是否已过期
  bool get isExpired => status == 3;
}