import 'response_parse_strategy.dart';
import '../http_base_response.dart';
import '../http_error_code.dart';
import '../../utils/log_util.dart';

/// 直接数据格式解析策略
class DirectDataStrategy<T> extends ResponseParseStrategy<T> {
  @override
  BaseResponse<T> parseJsonData(dynamic jsonData, T Function(dynamic) fromJsonT) {
    // 直接解析数据，认为都是成功的
    final parsedData = fromJsonT(jsonData);
    return BaseResponse<T>(
      code: 200,
      message: 'OK',
      data: parsedData,
    );
  }

  @override
  String get strategyName => 'DirectDataStrategy';
}