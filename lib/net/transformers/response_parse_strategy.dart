import 'dart:convert';
import '../http_base_response.dart';
import '../http_error_code.dart';
import '../../utils/log_util.dart';

/// 响应解析策略抽象类
abstract class ResponseParseStrategy<T> {
  /// 解析响应数据
  BaseResponse<T> parse(dynamic responseData, T Function(dynamic) fromJsonT) {
    try {
      // 如果responseData是字符串，先转换为JSON
      final jsonData = _convertToJsonData(responseData);
      if (jsonData == null) {
        return BaseResponse.error(
          'JSON解析失败',
          code: ErrorCode.parseError,
        );
      }
      
      // 调用具体策略的解析方法
      return parseJsonData(jsonData, fromJsonT);
    } catch (e) {
      LogUtil.e('响应解析失败', error: e);
      return BaseResponse.error(
        '解析失败: $e',
        code: ErrorCode.parseError,
      );
    }
  }

  /// 具体策略需要实现的JSON数据解析方法
  BaseResponse<T> parseJsonData(dynamic jsonData, T Function(dynamic) fromJsonT);

  /// 获取策略名称
  String get strategyName;

  /// 将响应数据转换为JSON对象
  dynamic _convertToJsonData(dynamic responseData) {
    if (responseData is String) {
      try {
        return jsonDecode(responseData);
      } catch (e) {
        LogUtil.e('JSON解析失败: $responseData', error: e);
        return null;
      }
    } else {
      return responseData;
    }
  }
}