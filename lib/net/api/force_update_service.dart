import 'package:flutter/material.dart';
import '../../utils/log_util.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../config/http_base_config.dart';
import '../../model/force_update_response.dart';
import 'init_service.dart';

class ForceUpdateService {
  static final ForceUpdateService _instance = ForceUpdateService._internal();
  factory ForceUpdateService() => _instance;
  ForceUpdateService._internal();

  /// 强更检查接口
  /// 检查应用是否需要强制更新
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<ForceUpdateResponse>> checkForceUpdate({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = '/go/cfg/app/update';
      
      // 获取公共参数
      final commonParams = await InitService.getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('强更检查请求: $url, 参数: $commonParams');

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<ForceUpdateResponse>(
        url,
        baseUrl: HttpBaseConfig.mActivateBaseUrl,
        data: commonParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return ForceUpdateResponse.fromJson(data);
          } else {
            throw Exception('响应数据格式错误，期望Map<String, dynamic>，实际: ${data.runtimeType}');
          }
        },
      );

      LogUtil.d('强更检查响应: ${response.toString()}');
      return response;
    } catch (e, stackTrace) {
      LogUtil.e('强更检查异常', error: e, stackTrace: stackTrace);
      return BaseResponse<ForceUpdateResponse>(
        code: -1,
        message: '强更检查失败: ${e.toString()}',
        data: null,
      );
    }
  }
}