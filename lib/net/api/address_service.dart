import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../model/address_list.dart';
import '../../providers/user_provider.dart';
import '../../utils/log_util.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../config/http_base_config.dart';

class AddressService {
  static final AddressService _instance = AddressService._internal();
  factory AddressService() => _instance;
  AddressService._internal();
  static  getAppkit(BuildContext? context){
    final ticket = context?.read<UserProvider>().currentTicket;
    return ticket;
  }


  /// 获取地址列表接口
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<AddressListResponse>> getAddressList({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'address/get_list';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(context),
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<AddressListResponse>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return AddressListResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<AddressListResponse>(),
      );

      LogUtil.d('地址列表响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('地址列表请求失败: $e');
      return BaseResponse<AddressListResponse>(
        code: 500,
        message: '地址列表请求失败: $e',
        data: null,
      );
    }
  }

  /// 新增地址接口
  /// [contact_name] 联系人
  /// [phone_country_code] +86
  /// [phone] 手机号码
  /// [province] 省份
  /// [city] 城市
  /// [district] 区
  /// [detail_address] 详细地址
  static Future<BaseResponse<String?>> addNewAddress({
    BuildContext? context,
    required String contact_name,
    required String phone_country_code,
    required String phone,
    required String province,
    required String city,
    required String district,
    required String detail_address,
  }) async {
    try {
      // 接口URL
      const url = 'address/add';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(context),
        'contact_name': contact_name,
        'phone_country_code': phone_country_code,
        'phone': phone,
        'province': province,
        'city': city,
        'district': district,
        'detail_address': detail_address,
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<String?>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) => null, // 响应没有data，直接返回null
        parseStrategy: ResponseTransformers.stateData<String?>(),
      );

      LogUtil.d('新增地址响应: ${response.code}, 消息: ${response.message}');
      return response;
    } catch (e) {
      LogUtil.e('新增地址请求失败: $e');
      return BaseResponse<String?>(
        code: 500,
        message: '新增地址请求失败: $e',
        data: null,
      );
    }
  }

  /// 更新地址接口
  /// [address_id] 地址ID
  /// [contact_name] 联系人
  /// [phone_country_code] +86
  /// [phone] 手机号码
  /// [province] 省份
  /// [city] 城市
  /// [district] 区
  /// [detail_address] 详细地址
  static Future<BaseResponse<String?>> updateAddress({
    BuildContext? context,
    required String address_id,
    required String contact_name,
    required String phone_country_code,
    required String phone,
    required String province,
    required String city,
    required String district,
    required String detail_address,
  }) async {
    try {
      // 接口URL
      const url = 'address/edit';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(context),
        'address_id': address_id,
        'contact_name': contact_name,
        'phone_country_code': phone_country_code,
        'phone': phone,
        'province': province,
        'city': city,
        'district': district,
        'detail_address': detail_address,
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<String?>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) => null, // 响应没有data，直接返回null
        parseStrategy: ResponseTransformers.stateData<String?>(),
      );

      LogUtil.d('更新地址响应: ${response.code}, 消息: ${response.message}');
      return response;
    } catch (e) {
      LogUtil.e('更新地址请求失败: $e');
      return BaseResponse<String?>(
        code: 500,
        message: '更新地址请求失败: $e',
        data: null,
      );
    }
  }

  /// 删除地址接口
  /// [address_id] 地址ID
  static Future<BaseResponse<String?>> deleteAddress({
    BuildContext? context,
    required int address_id,
  }) async {
    try {
      // 接口URL
      const url = 'address/del';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(context),
        'address_id': address_id,
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<String?>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) => null, // 响应没有data，直接返回null
        parseStrategy: ResponseTransformers.stateData<String?>(),
      );

      LogUtil.d('删除地址响应: ${response.code}, 消息: ${response.message}');
      return response;
    } catch (e) {
      LogUtil.e('删除地址请求失败: $e');
      return BaseResponse<String?>(
        code: 500,
        message: '删除地址请求失败: $e',
        data: null,
      );
    }
  }

  /// 设置默认地址接口
  /// [address_id] 地址ID
  static Future<BaseResponse<String?>> setDefaultAddress({
    BuildContext? context,
    required int address_id,
  }) async {
    try {
      // 接口URL
      const url = 'address/set_default';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(context),
        'address_id': address_id,
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<String?>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) => null, // 响应没有data，直接返回null
        parseStrategy: ResponseTransformers.stateData<String?>(),
      );
      LogUtil.d('设置默认地址响应: ${response.code}, 消息: ${response.message}');
      return response;
    } catch (e) {
      LogUtil.e('设置默认地址请求失败: $e');
      return BaseResponse<String?>(
        code: 500,
        message: '设置默认地址请求失败: $e',
        data: null,
      );
    }
  }

  /// 获取默认地址接口
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<DefaultAddressResponse>> getDefaultAddress({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'address/get_default';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(context),
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<DefaultAddressResponse>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return DefaultAddressResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<DefaultAddressResponse>(),
      );

      LogUtil.d('获取默认地址响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('获取默认地址请求失败: $e');
      return BaseResponse<DefaultAddressResponse>(
        code: 500,
        message: '获取默认地址请求失败: $e',
        data: null,
      );
    }
  }


}