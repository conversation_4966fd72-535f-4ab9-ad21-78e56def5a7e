import 'package:dlyz_flutter/config/app_config.dart';
import 'package:dio/dio.dart';
import '../utils/device_info_util.dart';
import '../utils/enhanced_device_info_util.dart';
import '../providers/user_provider.dart';
import 'package:provider/provider.dart';

/// 通用参数拦截器
/// 自动添加通用参数（移除签名逻辑，签名由SignInterceptor处理）
/// 支持GET、POST、PUT、DELETE等所有HTTP方法
class CommonParamsInterceptor extends Interceptor {
  final UserProvider? userProvider;

  CommonParamsInterceptor({this.userProvider});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // 获取设备信息
      final deviceInfo = await DeviceInfoUtil.getDeviceInfo();
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      
      // 获取应用版本号
      final appVersion = await EnhancedDeviceInfoUtil.getVersionName();

      // 构建通用参数
      final commonParams = <String, dynamic>{
        'pid': AppConfig.pid, // 产品ID，根据项目名称设置
        'gid': AppConfig.gid, // 游戏ID，可配置
        'appid': AppConfig.appId,
        'sversion': AppConfig.sversion,
        'gwversion': AppConfig.gwversion,
        'refer': AppConfig.refer,
        'host_sdk_version': AppConfig.hostSdkVersion,
        'os': deviceInfo['os'],
        'dev': await DeviceInfoUtil.getDev(),
        'time': timestamp,
        'version': appVersion,
        'scut': AppConfig.refer,
        'from': deviceInfo['os'],
        'idfa': await DeviceInfoUtil.getIDFA(),
        'idfv': await DeviceInfoUtil.getIDFV(),
      };
      
      // 添加用户票据（如果用户已登录）
      final userTicket = userProvider?.currentTicket;
      if (userTicket != null && userTicket.isNotEmpty) {
        commonParams['app_ticket'] = userTicket;
      }
      
      // 添加平台特定参数
      if (deviceInfo['os'] == 'android') {
        commonParams['oaid'] = deviceInfo['oaid'] ?? '';
      }
      
      final method = options.method.toUpperCase();
      
      if (method == 'GET') {
        // GET请求：参数添加到查询字符串中
        _handleGetRequest(options, commonParams);
      } else if (method == 'POST' || method == 'PUT' || method == 'PATCH') {
        // POST/PUT/PATCH请求：根据Content-Type处理
        _handlePostRequest(options, commonParams);
      } else if (method == 'DELETE') {
        // DELETE请求：可能在查询字符串或body中，优先查询字符串
        if (options.data == null || (options.data is Map && (options.data as Map).isEmpty)) {
          _handleGetRequest(options, commonParams);
        } else {
          _handlePostRequest(options, commonParams);
        }
      } else {
        // 其他HTTP方法，默认按GET处理
        _handleGetRequest(options, commonParams);
      }
      
      handler.next(options);
    } catch (e) {
      print('CommonParamsInterceptor error: $e');
      handler.next(options);
    }
  }
  
  /// 处理GET请求，参数添加到查询字符串
  void _handleGetRequest(RequestOptions options, Map<String, dynamic> commonParams) {
    // 合并现有查询参数和通用参数
    final Map<String, dynamic> allParams = Map<String, dynamic>.from(options.queryParameters);
    allParams.addAll(commonParams);
    
    // 更新查询参数（移除签名逻辑）
    options.queryParameters = allParams;
    
    print('GET请求参数: ${options.queryParameters}');
  }
  
  /// 处理POST/PUT/PATCH请求，根据Content-Type决定参数位置
  void _handlePostRequest(RequestOptions options, Map<String, dynamic> commonParams) {
    final contentType = options.headers['Content-Type']?.toString().toLowerCase() ?? '';
    
    if (contentType.contains('application/json')) {
      // JSON格式：参数添加到请求体
      _handleJsonRequest(options, commonParams);
    } else if (contentType.contains('application/x-www-form-urlencoded')) {
      // 表单格式：参数添加到请求体
      _handleFormRequest(options, commonParams);
    } else if (contentType.contains('multipart/form-data')) {
      // 文件上传格式：参数添加到FormData
      _handleMultipartRequest(options, commonParams);
    } else {
      // 默认处理方式：尝试智能判断
      _handleDefaultRequest(options, commonParams);
    }
  }
  
  /// 处理JSON格式请求
  void _handleJsonRequest(RequestOptions options, Map<String, dynamic> commonParams) {
    Map<String, dynamic> allParams = {};
    
    // 添加通用参数
    allParams.addAll(commonParams);
    
    // 添加原始请求参数
    if (options.data != null) {
      if (options.data is Map<String, dynamic>) {
        allParams.addAll(options.data as Map<String, dynamic>);
      } else if (options.data is String) {
        // 尝试解析JSON字符串
        try {
          final decoded = Uri.splitQueryString(options.data as String);
          allParams.addAll(decoded);
        } catch (e) {
          print('Failed to parse request data as query string: $e');
        }
      }
    }
    
    // 更新请求数据（移除签名逻辑）
    options.data = allParams;
    
    print('JSON请求参数: ${options.data}');
  }
  
  /// 处理表单格式请求
  void _handleFormRequest(RequestOptions options, Map<String, dynamic> commonParams) {
    Map<String, dynamic> allParams = {};
    
    // 添加通用参数
    allParams.addAll(commonParams);
    
    // 添加原始请求参数
    if (options.data != null) {
      if (options.data is Map<String, dynamic>) {
        allParams.addAll(options.data as Map<String, dynamic>);
      } else if (options.data is String) {
        // 解析表单编码的字符串参数
        final String dataString = options.data as String;
        final pairs = dataString.split('&');
        for (final pair in pairs) {
          final parts = pair.split('=');
          if (parts.length == 2) {
            allParams[Uri.decodeComponent(parts[0])] = Uri.decodeComponent(parts[1]);
          }
        }
      }
    }
    
    // 更新请求数据（移除签名逻辑，不再转换为表单编码格式）
    options.data = allParams;
    
    print('表单请求参数: ${options.data}');
  }
  
  /// 处理文件上传格式请求
  void _handleMultipartRequest(RequestOptions options, Map<String, dynamic> commonParams) {
    FormData formData;
    
    if (options.data is FormData) {
      formData = options.data as FormData;
    } else {
      formData = FormData();
      
      // 如果原始数据不是FormData，尝试添加原始参数
      if (options.data is Map<String, dynamic>) {
        for (final entry in (options.data as Map<String, dynamic>).entries) {
          formData.fields.add(MapEntry(entry.key.toString(), entry.value.toString()));
        }
      }
    }
    
    // 添加通用参数到FormData
    for (final entry in commonParams.entries) {
      formData.fields.add(MapEntry(entry.key.toString(), entry.value.toString()));
    }
    
    // 更新请求数据
    options.data = formData;
    
    print('文件上传请求参数: ${formData.fields}');
  }
  
  /// 默认处理方式，智能判断参数格式
  void _handleDefaultRequest(RequestOptions options, Map<String, dynamic> commonParams) {
    if (options.data == null) {
      // 没有请求体数据，按JSON格式处理
      options.headers['Content-Type'] = 'application/json';
      _handleJsonRequest(options, commonParams);
    } else if (options.data is FormData) {
      // FormData格式
      options.headers['Content-Type'] = 'multipart/form-data';
      _handleMultipartRequest(options, commonParams);
    } else if (options.data is String && (options.data as String).contains('=')) {
      // 看起来像表单编码
      options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
      _handleFormRequest(options, commonParams);
    } else {
      // 默认按JSON处理
      options.headers['Content-Type'] = 'application/json';
      _handleJsonRequest(options, commonParams);
    }
  }
}