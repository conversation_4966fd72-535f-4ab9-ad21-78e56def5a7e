import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:encrypt/encrypt.dart';
import '../utils/md5_utils.dart';

/// 网关加密拦截器
/// 实现统一网关加密功能，包括请求加密和响应解密
class GateWayEncryptInterceptor extends Interceptor {
  final GateWayProvider _provider;
  final Function(String, int, String, String)? _exceptionReporter;

  GateWayEncryptInterceptor({
    required GateWayProvider provider,
    Function(String, int, String, String)? exceptionReporter,
  }) : _provider = provider,
       _exceptionReporter = exceptionReporter;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // 所有请求都进行加密处理
      final encryptedOptions = await _encryptRequest(options);
      handler.next(encryptedOptions);
    } catch (e) {
      // 加密失败时降级处理
      _reportException(e, 399, '统一网关加密异常', _buildErrorData(options.uri.toString()));
      handler.next(options);
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    try {
      // 所有响应都进行解密处理
      final decryptedResponse = await _decryptResponse(response);
      handler.next(decryptedResponse);
    } catch (e) {
      // 解密失败时降级处理
      _reportException(e, 399, '统一网关解密异常', _buildErrorData(response.requestOptions.uri.toString()));
      handler.next(response);
    }
  }

  /// 加密请求
  Future<RequestOptions> _encryptRequest(RequestOptions originalOptions) async {
    final key = _provider.provideKey();
    final version = _provider.provideXRequestVersion();
    final xRequestId = _obtainXRequestId(originalOptions);
    final nonceStr = _buildNonceStr(originalOptions, xRequestId);

    // 构建新的请求选项
    final headers = Map<String, dynamic>.from(originalOptions.headers)
      ..addAll({
        'x-request-id': xRequestId,
        'X-Request-Nonce-Str': nonceStr,
        'x-request-version': version,
      })
      ..remove('Request-Id');

    // 加密查询参数
    String? encryptedQuery;
    final queryParams = originalOptions.uri.query;
    if (queryParams.isNotEmpty) {
      encryptedQuery = await _encrypt(nonceStr, queryParams, key);
    }

    // 加密请求体
    dynamic encryptedData;
    if (originalOptions.data != null) {
      final contentType = originalOptions.headers['content-type'] ??
          originalOptions.headers['Content-Type'];
      String requestBodyString = originalOptions.data.toString();
      if (contentType != null &&
          contentType.toString().toLowerCase().contains('application/x-www-form-urlencoded')) {
        // 将Map转换为form格式字符串
        if (originalOptions.data is Map) {
          final formData = originalOptions.data as Map;
          final formEntries = formData.entries.map((entry) =>
          '${Uri.encodeComponent(entry.key.toString())}=${Uri.encodeComponent(entry.value.toString())}'
          );
          requestBodyString = formEntries.join('&');
        } else {
          requestBodyString = originalOptions.data.toString();
        }
      } else {
        requestBodyString = originalOptions.data.toString();
      }
      encryptedData = await _encrypt(nonceStr, requestBodyString, key);
    }

    // 构建新的URI
    Uri newUri = originalOptions.uri;
    if (encryptedQuery != null) {
      newUri = Uri.parse('${_buildUrlWithoutQuery(originalOptions.uri.toString())}?$encryptedQuery');
    }

    // 计算相对路径，避免URL重复拼接
    String relativePath = _getRelativePath(originalOptions.baseUrl, newUri.path);

    return originalOptions.copyWith(
      headers: headers,
      data: encryptedData ?? originalOptions.data,
      path: relativePath,
      queryParameters: newUri.queryParameters,
    );
  }

  /// 解密响应
  Future<Response> _decryptResponse(Response encryptedResponse) async {
    final key = _provider.provideKey();
    final responseNonceStr = encryptedResponse.headers.value('x-response-nonce-str');
    final xRequestNonceStr = encryptedResponse.requestOptions.headers['X-Request-Nonce-Str'];

    if (xRequestNonceStr == null || responseNonceStr == null) {
      return encryptedResponse;
    }

    final encryptKey = key + xRequestNonceStr.substring(0, 8) + responseNonceStr.substring(0, 8);
    final ivStr = responseNonceStr.substring(8, 24);
    final ivParameterSpec = _generateIv(ivStr);

    if (encryptedResponse.data == null) {
      return encryptedResponse;
    }

    final responseBody = encryptedResponse.data.toString();
    final decryptStr = _decryptUrlSafe(responseBody, encryptKey, ivParameterSpec);
    
    return Response(
      data: decryptStr,
      statusCode: encryptedResponse.statusCode,
      statusMessage: encryptedResponse.statusMessage,
      headers: encryptedResponse.headers,
      requestOptions: encryptedResponse.requestOptions,
      isRedirect: encryptedResponse.isRedirect,
      redirects: encryptedResponse.redirects,
      extra: encryptedResponse.extra,
    );
  }



  /// 加密逻辑
  /// 1. 使用固定Key(16位) + 请求加密随机串(前16位) 组成32位的AES签名KEY
  /// 2. AES初始向量IV 使用 请求加密随机串(后16位)
  /// 3. 使用 AES算法 CBC模式 PKCS7填充模式 对HTTP请求参数和HTTP请求体进行加密
  /// 4. HTTP请求参数和HTTP请求体加密后要再使用base64url(不填充块)进行编码
  Future<String> _encrypt(String nonceStr, String text, String key) async {
    final encryptKey = key + nonceStr.substring(0, 16);
    final ivStr = nonceStr.substring(nonceStr.length - 16);
    final ivParameterSpec = _generateIv(ivStr);
    
    final encrypted = _encryptText(text, encryptKey, ivParameterSpec);
    return _toBase64Url(encrypted);
  }

  /// 解密URL安全的Base64编码
  String _decryptUrlSafe(String encryptedBase64Url, String key, IV iv) {
    final encryptedBytes = _fromBase64Url(encryptedBase64Url);
    return _decryptText(encryptedBytes, key, iv);
  }

  /// AES加密
  Uint8List _encryptText(String text, String key, IV iv) {
    final aesKey = Key.fromUtf8(key.padRight(32).substring(0, 32));
    final encrypter = Encrypter(AES(aesKey, mode: AESMode.cbc, padding: 'PKCS7'));
    final encrypted = encrypter.encrypt(text, iv: iv);
    return encrypted.bytes;
  }

  /// AES解密
  String _decryptText(Uint8List encryptedBytes, String key, IV iv) {
    final aesKey = Key.fromUtf8(key.padRight(32).substring(0, 32));
    final encrypter = Encrypter(AES(aesKey, mode: AESMode.cbc, padding: 'PKCS7'));
    final encrypted = Encrypted(encryptedBytes);
    return encrypter.decrypt(encrypted, iv: iv);
  }

  /// 生成IV
  IV _generateIv(String ivStr) {
    return IV.fromUtf8(ivStr.padRight(16).substring(0, 16));
  }

  /// 转换为Base64URL编码（不填充）
  String _toBase64Url(Uint8List bytes) {
    final base64 = base64Encode(bytes);
    return base64
        .replaceAll('+', '-')
        .replaceAll('/', '_')
        .replaceAll('=', '');
  }

  /// 从Base64URL解码
  Uint8List _fromBase64Url(String base64Url) {
    String base64 = base64Url
        .replaceAll('-', '+')
        .replaceAll('_', '/');
    
    // 添加填充
    final padding = 4 - (base64.length % 4);
    if (padding != 4) {
      base64 += '=' * padding;
    }
    
    return base64Decode(base64);
  }

  /// 构造请求随机签名串
  String _buildNonceStr(RequestOptions request, String xRequestId) {
    final signBuilder = StringBuffer();
    
    // 拼接请求方法
    signBuilder.write(request.method);
    
    // 拼接请求参数
    final queryParams = request.uri.query;
    if (queryParams.isNotEmpty) {
      signBuilder.write(queryParams);
    }

    // 处理拼接body
    if (request.data != null) {
      // 检查content-type是否为application/x-www-form-urlencoded
      final contentType = request.headers['content-type'] ?? 
                         request.headers['Content-Type'];
      
      if (contentType != null && 
          contentType.toString().toLowerCase().contains('application/x-www-form-urlencoded')) {
        // 将Map转换为form格式字符串
        if (request.data is Map) {
          final formData = request.data as Map;
          final formEntries = formData.entries.map((entry) => 
            '${Uri.encodeComponent(entry.key.toString())}=${Uri.encodeComponent(entry.value.toString())}'
          );
          signBuilder.write(formEntries.join('&'));
        } else {
          signBuilder.write(request.data.toString());
        }
      } else {
        signBuilder.write(request.data.toString());
      }
    }

    // 拼接Cookie
    final cookie = request.headers['Cookie'];
    if (cookie != null && cookie.isNotEmpty) {
      signBuilder.write(cookie);
    }

    // 拼接Authorization
    final authorization = request.headers['Authorization'];
    if (authorization != null && authorization.isNotEmpty) {
      signBuilder.write(authorization);
    }

    signBuilder.write(xRequestId);
    
    // 取md5后的前32位
    return Md5Utils.generateMd5(signBuilder.toString()).substring(0, 32);
  }

  /// 获取X-Request-ID
  String _obtainXRequestId(RequestOptions request) {
    String requestId = request.headers['Request-Id'] ?? '';
    if (requestId.isEmpty) {
      requestId = _generateRequestId();
    }
    return Md5Utils.generateMd5(requestId);
  }

  /// 生成请求ID
  String _generateRequestId() {
    final random = Random();
    final randomData = String.fromCharCodes(
      List<int>.generate(16, (i) => random.nextInt(256))
    );
    
    return 'flutter-${DateTime.now().millisecondsSinceEpoch}-$randomData';
  }

  /// 构建URL（去除查询参数）
  String _buildUrlWithoutQuery(String url) {
    final uri = Uri.parse(url);
    return '${uri.scheme}://${uri.authority}${uri.path}';
  }

  /// 计算相对路径，避免URL重复拼接
  String _getRelativePath(String baseUrl, String fullPath) {
    try {
      // 解析baseUrl获取其路径部分
      final baseUri = Uri.parse(baseUrl);
      final basePath = baseUri.path;
      
      // 如果fullPath以basePath开头，则移除basePath部分
      if (basePath.isNotEmpty && fullPath.startsWith(basePath)) {
        // 移除basePath，保留相对路径
        String relativePath = fullPath.substring(basePath.length);
        // 确保相对路径不以'/'开头（除非basePath以'/'结尾）
        if (basePath.endsWith('/') && relativePath.startsWith('/')) {
          relativePath = relativePath.substring(1);
        }
        return relativePath;
      }
      
      // 如果不匹配，返回原始路径
      return fullPath;
    } catch (e) {
      // 发生错误时返回原始路径
      return fullPath;
    }
  }

  /// 构建错误数据
  String _buildErrorData(String url) {
    try {
      final json = {'url': url};
      return jsonEncode(json);
    } catch (e) {
      return 'build data error $e';
    }
  }

  /// 上报异常
  void _reportException(dynamic exception, int code, String title, String data) {
    if (_exceptionReporter != null) {
      _exceptionReporter!(title, code, exception.toString(), data);
    }
  }
}

/// 网关提供者接口
abstract class GateWayProvider {
  /// 提供加密的固定key，不可为空
  String provideKey();

  /// 提供加密网关密钥版本，字符串为默认密钥
  String provideXRequestVersion();
}

/// 默认网关提供者实现
/// 基于Android项目中的配置和密钥策略
class DefaultGateWayProvider implements GateWayProvider {
  static const String _defaultKey = '2384039739279483'; // 16位默认密钥，来自AesUtil
  static const String _defaultVersion = 'v1.0';
  
  final String _key;
  final String _version;

  DefaultGateWayProvider({
    String? key,
    String? version,
  }) : _key = key ?? _defaultKey,
       _version = version ?? _defaultVersion;

  @override
  String provideKey() => _key;

  @override
  String provideXRequestVersion() => _version;
}

/// 37游戏专用网关提供者
/// 基于Android项目中的37游戏配置
class Game37GateWayProvider implements GateWayProvider {
  static const String _game37Key = 'soC2GAr8jN2fsbry'; // 37游戏专用密钥
  static const String _game37Version = '';
  
  final String _key;
  final String _version;

  Game37GateWayProvider({
    String? key,
    String? version,
  }) : _key = key ?? _game37Key,
       _version = version ?? _game37Version;

  @override
  String provideKey() => _key;

  @override
  String provideXRequestVersion() => _version;
}

 