import 'dart:io';
import 'package:dlyz_flutter/zhifu/zhifu_strategy_interface.dart';
import 'package:dlyz_flutter/zhifu/zhifu_type.dart';
import 'package:dlyz_flutter/zhifu/strategies/android_alizhifu_strategy.dart';
import 'package:dlyz_flutter/zhifu/strategies/android_wechat_zhifu_strategy.dart';
import 'package:dlyz_flutter/zhifu/strategies/ios_alizhifu_strategy.dart';
import 'package:dlyz_flutter/zhifu/strategies/ios_wechat_zhifu_strategy.dart';

class ZhiFuManager {
  static final ZhiFuManager _instance = ZhiFuManager._internal();

  factory ZhiFuManager() => _instance;

  ZhiFuManager._internal();

  static getInstance() {
    return _instance;
  }

  final Map<ZhiFuStrategyType, IZhiFuStrategy> _payStrategyMap = {
    ZhiFuStrategyType.alizhifuAndroid: AndroidAlipayStrategy(),
    ZhiFuStrategyType.alizhifuIos: IosAlipayStrategy(),
    ZhiFuStrategyType.wxzhifuAndroid: AndroidWechatPayStrategy(),
    ZhiFuStrategyType.wxzhifuIos: IosWechatPayStrategy(),
  };

  Future<Map<String, String>> zhiFu(Map<String, dynamic> params) async {
    String zhiFuType = params['zhiFuType'] ?? '';
    ZhiFuType? zhiFuEnum;
    try {
      zhiFuEnum = ZhiFuType.values.firstWhere((e) => e.name == zhiFuType);
    } catch (e) {
      zhiFuEnum = null;
    }
    if (zhiFuEnum == null) {
      return ZhiFuResult.buildReturnResult(ZhiFuResult.ZHIFU_FAILED, '不支持的支付类型');
    }
    IZhiFuStrategy? zhiFuStrategy = _getZhiFuStrategy(zhiFuEnum);
    if (zhiFuStrategy != null) {
      return await zhiFuStrategy.zhiFu(params);
    }
    return ZhiFuResult.buildReturnResult(ZhiFuResult.ZHIFU_FAILED, '不支持的支付类型');
  }

  ///根据类型获取对应支付策略
  IZhiFuStrategy? _getZhiFuStrategy(ZhiFuType zhiFuType) {
    if (Platform.isAndroid) {
      if (zhiFuType == ZhiFuType.alizhifu) {
        return _payStrategyMap[ZhiFuStrategyType.alizhifuAndroid];
      } else if (zhiFuType == ZhiFuType.wxzhifu) {
        return _payStrategyMap[ZhiFuStrategyType.wxzhifuAndroid];
      }
      return null;
    } else {
      if (zhiFuType == ZhiFuType.alizhifu) {
        return _payStrategyMap[ZhiFuStrategyType.alizhifuIos];
      } else if (zhiFuType == ZhiFuType.wxzhifu) {
        return _payStrategyMap[ZhiFuStrategyType.wxzhifuIos];
      }
      return null;
    }
  }
}
