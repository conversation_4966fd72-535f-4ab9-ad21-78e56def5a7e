import '../../manager/channel_manager.dart';
import '../zhifu_strategy_interface.dart';
import '../zhifu_type.dart';

class AndroidAlipayStrategy implements IZhiFuStrategy {
  @override
  Future<Map<String, String>> zhiFu(Map<String, dynamic> params) async {
    String orderId = params['uuid'] ?? '';
    String tradeInfo = params['trade'] ?? '';
    if (tradeInfo.isEmpty) {
      return ZhiFuResult.buildReturnResult(ZhiFuResult.ZHIFU_FAILED, '支付信息不能为空');
    }
    ZhiFuResult zhiFuResult = await ChannelManager().androidZhiFu(
      zhiFuType: ZhiFuType.alizhifu,
      orderId: orderId,
      tradeInfo: tradeInfo,
    );
    return ZhiFuResult.buildReturnResult(zhiFuResult.code, zhiFuResult.message);
  }
}
