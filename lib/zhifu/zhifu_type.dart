enum ZhiFuType { alizhifu, wxzhifu }

enum ZhiFuStrategyType { alizhifuAndroid, alizhifuIos, wxzhifuAndroid, wxzhifuIos }

class ZhiFuResult {

  static final String ZHIFU_SUCCESS = 'ZHIFU_SUCCESS';
  static final String ZHIFU_FAILED = 'ZHIFU_FAILED';
  static final String ZHIFU_CANCELLED = 'ZHIFU_CANCELLED';
  static final String ZHIFU_UNKNOWN = 'ZHIFU_UNKNOWN';

  final String code;
  final String message;

  ZhiFuResult({required this.code, required this.message});

  static Map<String, String> buildReturnResult(String code, String message) {
    return {'code': code, 'message': message};
  }
}
