import 'dart:async';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/model/connectivity_result.dart';
import 'package:dlyz_flutter/utils/sp_utils.dart';

/// 网络访问控制状态枚举
enum NetworkAccessControl {
  /// 允许移动网络和Wi-Fi网络
  allowAll,

  /// 仅允许Wi-Fi网络
  wifiOnly,

  /// 关闭网络访问
  disabled
}

class NetworkManager {
  static final NetworkManager _instance = NetworkManager._internal();
  factory NetworkManager() => _instance;
  NetworkManager._internal();

  // 网络访问控制状态的SharedPreferences键名
  static const String _NETWORK_ACCESS_CONTROL_KEY = 'network_access_control';

  final ChannelManager _channelManager = ChannelManager();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  List<ConnectivityResult>? _lastConnectivityResult;
  
  // 网络访问控制状态变量，默认为允许所有网络
  NetworkAccessControl _networkAccessControl = NetworkAccessControl.allowAll;

  /// 初始化网络监听
  void addListener({Function(List<ConnectivityResult>)? onConnectivityChanged}) {
    // 初始化时从SharedPreferences读取网络访问控制状态
    _loadNetworkAccessControlState();
    
    _connectivitySubscription = _channelManager.onConnectivityChanged.listen((List<ConnectivityResult> result) {
      if (!_listsEqual(_lastConnectivityResult, result)) {
        _lastConnectivityResult = List.from(result);
        if (onConnectivityChanged != null) {
          onConnectivityChanged(result);
        }
      }
    });
  }

  /// 销毁网络监听
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
  }

  /// 获取当前网络状态（异步）
  Future<List<ConnectivityResult>> checkNetworkConnectivity() async {
    _lastConnectivityResult = await _channelManager.checkNetworkConnectivity();
    return _lastConnectivityResult!;
  }

  /// 获取最近一次的网络状态（同步）
  /// 注意：如果还没有初始化或者没有监听到网络状态，可能返回 null
  List<ConnectivityResult>? getCurrentConnectivity() {
    return _lastConnectivityResult;
  }

  /// 获取当前网络访问控制状态
  NetworkAccessControl getNetworkAccessControl() {
    return _loadNetworkAccessControlState();
  }

  /// 设置网络访问控制状态
  void setNetworkAccessControl(NetworkAccessControl control) {
    _networkAccessControl = control;
    // 保存状态到SharedPreferences
    _saveNetworkAccessControlState();
  }

  /// 检查给定的网络连接是否被允许
  bool isConnectionAllowed(List<ConnectivityResult> connectivityResults) {
    // 如果网络访问控制已禁用，不允许任何连接
    if (_networkAccessControl == NetworkAccessControl.disabled) {
      return false;
    }

    // 如果设置为仅允许Wi-Fi，检查当前连接是否包含移动网络
    if (_networkAccessControl == NetworkAccessControl.wifiOnly) {
      // 检查是否只有Wi-Fi连接，没有移动网络
      bool hasWifi = connectivityResults.contains(ConnectivityResult.wifi);
      bool hasMobile = connectivityResults.contains(ConnectivityResult.mobile);
      
      // 允许仅Wi-Fi连接或者同时有Wi-Fi和移动网络（但优先使用Wi-Fi）
      return hasWifi && !hasMobile;
    }

    // 允许所有网络连接
    return true;
  }

  /// 从SharedPreferences加载网络访问控制状态
  NetworkAccessControl _loadNetworkAccessControlState() {
    try {
      int? savedValue = SpManager.getInstance().getInt(_NETWORK_ACCESS_CONTROL_KEY);
      if (savedValue != null) {
        _networkAccessControl = NetworkAccessControl.values[savedValue];
      } else {
        // 默认状态为允许所有网络
        _networkAccessControl = NetworkAccessControl.allowAll;
      }
    } catch (e) {
      // 如果读取失败，使用默认状态
      _networkAccessControl = NetworkAccessControl.allowAll;
    }
    return _networkAccessControl;
  }

  /// 保存网络访问控制状态到SharedPreferences
  void _saveNetworkAccessControlState() {
    try {
      SpManager.getInstance().put(_NETWORK_ACCESS_CONTROL_KEY, _networkAccessControl.index);
    } catch (e) {
      // 忽略保存错误
      print('保存网络访问控制状态失败: $e');
    }
  }

  /// 比较两个列表是否相等
  bool _listsEqual(List<ConnectivityResult>? a, List<ConnectivityResult>? b) {
    if (a == null || b == null) return a == b;
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}