import 'dart:convert';
import 'package:dlyz_flutter/webview/handler/CloseHandler.dart';
import 'package:dlyz_flutter/webview/handler/GetBindingRoleDataHandler.dart';
import 'package:dlyz_flutter/webview/handler/IsHasPermissionHandler.dart';
import 'package:dlyz_flutter/webview/handler/JumpGameBindingPageHandler.dart';
import 'package:dlyz_flutter/webview/handler/JumpSpecialStoreHandler.dart';
import 'package:dlyz_flutter/webview/handler/OpenActionBrowserHandler.dart';
import 'package:dlyz_flutter/webview/handler/OpenAddAddressHandler.dart';
import 'package:dlyz_flutter/webview/handler/OpenEditAddressHandler.dart';
import 'package:dlyz_flutter/webview/handler/ZhiFuHandler.dart';
import 'package:dlyz_flutter/webview/handler/RequestPermissionHandler.dart';
import 'package:dlyz_flutter/webview/handler/ShowGameBindingDialogHandler.dart';
import 'package:dlyz_flutter/webview/handler/ShowToastHandler.dart';
import 'package:dlyz_flutter/webview/webview_bridge_interface.dart';
import 'package:dlyz_flutter/webview/webview_wrapper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'handler/OpenAddressListHandler.dart';
import 'handler/OpenWebviewHandler.dart';

/// JavaScript消息结构
class JSMessage {
  final String method;
  final Map<String, dynamic> data;

  JSMessage({
    required this.method,
    required this.data,
  });

  factory JSMessage.fromJson(Map<String, dynamic> json) {
    return JSMessage(
      method: json['method'] ?? '',
      data: json['data'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'data': data,
    };
  }
}

/// JavaScript通信桥接器
class JSBridge {
  /// 消息处理器映射
  final Map<String, IWebViewBridge> _handlers = {};
  
  /// 通用消息监听器
  Function(JSMessage)? onMessage;

  final BuildContext _context;

  WebViewWrapperController? _webViewController;

  JSBridge(this._context, {WebViewWrapperController? webViewController})
      : _webViewController = webViewController {
    _registerDefaultHandlers(_context, _webViewController);
  }

  /// 注册默认处理器
  void _registerDefaultHandlers(BuildContext context, WebViewWrapperController? webViewController) {
    final handlers = [
      // 跳转外部浏览器
      OpenActionBrowserHandler(context, webViewController),

      // Toast提示
      ShowToastHandler(context, webViewController),

      // 关闭页面
      CloseHandler(context, webViewController),

      //查询权限是否授权
      IsHasPermissionHandler(context, webViewController),

      //申请权限
      RequestPermissionHandler(context, webViewController),

      //拉起角色授权弹窗
      ShowGameBindingDialogHandler(context, webViewController),

      //获取绑定角色列表
      GetBindingRoleDataHandler(context, webViewController),

      //跳转游戏绑定页
      JumpGameBindingPageHandler(context, webViewController),

      //打开webview
      OpenWebviewHandler(context, webViewController),

      //支付
      ZhiFuHandler(context, webViewController),

      //跳转特惠商城，android跳转圈子，ios跳转特惠商城
      JumpSpecialStoreHandler(context, webViewController),

      // 新增地址页面
      OpenAddAddressHandler(context, webViewController),

      // 修改地址页面
      OpenEditAddressHandler(context, webViewController),

      // 新增地址页面
      OpenAddressListHandler(context, webViewController),
    ];
    registerHandlers(handlers);
  }

  void _executeJsMethod(String methodName, Map<String, dynamic> params) {
    if (_webViewController == null) return;
    _webViewController!.executeJavaScript("window.$methodName('${jsonEncode(params)}')");
  }

  /// 注册消息处理器
  void registerHandler(IWebViewBridge handler) {
    _handlers[handler.bridgeName()] = handler;
  }

  void registerHandlers(List<IWebViewBridge> handlers) {
    for (final handler in handlers) {
      registerHandler(handler);
    }
  }

  /// 移除消息处理器
  void unregisterHandler(String method) {
    _handlers.remove(method);
  }

  /// 处理来自WebView的消息
  void handleMessage(String message) {
    try {
      final Map<String, dynamic> messageMap = jsonDecode(message);
      final jsMessage = JSMessage.fromJson(messageMap);
      
      // 首先尝试特定的处理器
      if (onMessage != null) {
        onMessage!(jsMessage);
        return;
      }

      // 通用的处理器
      final handler = _handlers[jsMessage.method];
      if (handler != null) {
        handler.process(jsMessage);
        return;
      }
    } catch (e) {
      debugPrint('解析JavaScript消息失败: $e');
      debugPrint('原始消息: $message');
    }
  }
}