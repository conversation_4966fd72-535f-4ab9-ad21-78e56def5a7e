import 'dart:convert';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/widgets.dart';
import 'webview_wrapper.dart';

abstract class IWebViewBridge {
  final BuildContext context;
  final WebViewWrapperController? controller;

  /// 当bridge的handler无法复用的时候或者简单bridge，提供给外部重载函数
  final Function? handlerFunc;

  const IWebViewBridge(this.context, this.controller, {this.handlerFunc});

  void process(JSMessage message) {
    try {
      if (handlerFunc != null) {
        handlerFunc!(message);
        return;
      }
      handler(message);
    } catch (e) {
      LogUtil.e('处理JavaScript消息失败: $e');
    }
  }

  ///handlerFunc函数为空没有重载时，才会执行此方法
  void handler(JSMessage message);

  ///bridge名字
  String bridgeName();

  ///回调h5
  executeJsMethod(String methodName, Map<String, dynamic> params) {
    if (controller == null) return;
    try {
      controller!.executeJavaScript(
        "window.$methodName('${jsonEncode(params)}')",
      );
    } catch (e) {
      LogUtil.e('执行JavaScript方法失败: $e');
    }
  }
}
