import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../pages/bind/switch_bind_character_page.dart';
import '../../pages/community/switch_bind_role.dart';
import '../webview_bridge_interface.dart';

class JumpGameBindingPageHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "jumpGameBindingPage";

  JumpGameBindingPageHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    var result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SwitchBindCharacterPage()),
    );
    String callback = message.data["callback"] ?? '';
    if (callback.isNotEmpty) {
      final selectRole = SwitchBindRoleDialog.getSelectedRole(context);
      executeJsMethod(callback, {'selectRole': selectRole});
    }
  }
}
