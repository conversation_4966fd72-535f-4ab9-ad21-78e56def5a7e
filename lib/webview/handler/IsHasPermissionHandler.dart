import 'package:dlyz_flutter/webview/js_bridge.dart';

import '../../utils/log_util.dart';
import '../../utils/permission_utils.dart';
import '../webview_bridge_interface.dart';

class IsHasPermissionHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "isHasPermission";

  IsHasPermissionHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    String permission = message.data["permissionName"] ?? '';
    PermissionType? permissionType;
    if ('notification' == permission) {
      permissionType = PermissionType.notification;
    }
    if (permissionType == null) return;
    final isHasPermission = await PermissionUtils.isPermissionGranted(
      permissionType,
    );
    String callback = message.data["callback"] ?? '';
    LogUtil.d("isHasPermission: $isHasPermission, callback = $callback");
    if (callback.isNotEmpty) {
      executeJsMethod(callback, {'isHasPermission': isHasPermission});
    }
  }
}
