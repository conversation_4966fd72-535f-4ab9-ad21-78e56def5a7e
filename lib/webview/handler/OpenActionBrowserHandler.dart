import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:dlyz_flutter/webview/webview_bridge_interface.dart';
import '../../manager/channel_manager.dart';

class OpenActionBrowserHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "openActionBrowser";

  OpenActionBrowserHandler(super.context, super.controller, {super.handlerFunc});

  @override
  void handler(JSMessage message) {
    String url = message.data["url"] ?? '';
    if (url.isNotEmpty) {
      ChannelManager().openUrl(url: url);
    }
  }

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }
}
