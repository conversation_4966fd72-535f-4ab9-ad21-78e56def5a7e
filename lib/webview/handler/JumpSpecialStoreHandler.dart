import 'dart:io';

import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../model/function_area.dart';
import '../../pages/community/community_page.dart';
import '../../providers/function_area_provider.dart';
import '../../utils/log_util.dart';
import '../web_router.dart';
import '../webview_bridge_interface.dart';

class JumpSpecialStoreHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "jumpSpecialStore";

  JumpSpecialStoreHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) {
    if (Platform.isAndroid) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const CommunityPage()),
      );
    } else {
      FunctionArea? area = Provider.of<FunctionAreaProvider>(
        context,
        listen: false,
      ).findByName(FunctionModuleType.direct_pay.name);
      LogUtil.d(
        'FunctionModuleType.direct_pay.name = ${FunctionModuleType.direct_pay.name}',
      );
      if (area != null) {
        LogUtil.d('area.areaEntrance = ${area.areaEntrance}');
        WebRouter.jumpToWebPage(context, area.areaEntrance);
      } else {
        LogUtil.d('area is null');
      }
    }
  }
}
