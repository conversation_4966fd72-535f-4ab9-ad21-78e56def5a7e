import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../web_router.dart';
import '../webview_bridge_interface.dart';

class OpenWebviewHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "openWebview";

  OpenWebviewHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) {
    String url = message.data["url"] ?? '';
    final ticket = context.read<UserProvider>().currentTicket;
    Map<String, dynamic> params = {'app_ticket': ticket};
    if (url.isNotEmpty) {
      WebRouter.jumpToWebPage(context, url, params: params);
    }
  }
}
