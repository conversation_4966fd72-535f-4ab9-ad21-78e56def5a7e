import 'package:dlyz_flutter/webview/js_bridge.dart';

import '../../zhifu/zhifu_manager.dart';
import '../webview_bridge_interface.dart';

class ZhiFuHandler extends IWebViewBridge {

  static final String BRIDGE_NAME = "zhiFu";

  ZhiFuHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    Map<String, String> result = await ZhiFuManager.getInstance().zhiFu(message.data);
    String callback = message.data["callback"] ?? '';
    if (callback.isNotEmpty) {
      executeJsMethod(callback, result);
    }
  }

}