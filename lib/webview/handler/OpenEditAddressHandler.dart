import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/material.dart';
import '../../model/address_list.dart';
import '../../net/api/address_service.dart';
import '../../pages/settings/AddressManagementPage.dart';
import '../../utils/log_util.dart';
import '../webview_bridge_interface.dart';

class OpenEditAddressHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "openEditAddress";

  OpenEditAddressHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    String callback = message.data["callback"] ?? '';

    // 从消息中提取地址数据并封装为addressData
    Map<String, dynamic> addressData = {
      'address_id': message.data["address_id"] ?? -1,
      'contact_name': message.data["contact_name"] ?? '',
      'province': message.data["province"] ?? '',
      'phone': message.data["phone"] ?? '',
      'phone_country_code': message.data["phone_country_code"] ?? '+86',
      'district': message.data["district"] ?? '',
      'detail_address': message.data["detail_address"] ?? '',
      'city': message.data["city"] ?? '',
      'is_default_address': message.data["is_default_address"] ?? '',
    };

    // 检查必要的地址数据
    if (addressData['address_id'] == -1 ||
        addressData['contact_name'] == '' ||
        addressData['province'] == '' ||
        addressData['phone'] == '' ||
        addressData['phone_country_code'] == '' ||
        addressData['district'] == '' ||
        addressData['detail_address'] == '' ||
        addressData['city'] == '') {
      if (callback.isNotEmpty) {
        executeJsMethod(callback, {
          'success': false,
          'response': {'code': 400, 'message': '地址不完整', 'data': null},
        });
      }
      return;
    }

    try {
      // 构造地址对象
      final address = AddressList.fromJson(addressData);

      // 获取默认地址，判断当前地址是否为默认地址
      bool isDefaultAddress = false;
      if (addressData['is_default_address'] == 'true') {
        isDefaultAddress = true;
      } else if (addressData['is_default_address'] == '') {
        try {
          final defaultResponse = await AddressService.getDefaultAddress(
            context: context,
          );
          if (defaultResponse.success &&
              defaultResponse.data != null &&
              defaultResponse.data!.is_exist &&
              defaultResponse.data!.address != null) {
            isDefaultAddress =
                defaultResponse.data!.address!.address_id == address.address_id;
          }
        } catch (e) {
          LogUtil.e('获取默认地址失败: $e');
          // 默认地址获取失败不影响地址编辑功能，继续执行
        }
      }

      var result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => EditAddressPage(
                address: address,
                isDefaultAddress: isDefaultAddress,
              ),
        ),
      );

      if (callback.isNotEmpty) {
        if (result != null && result is Map<String, dynamic>) {
          executeJsMethod(callback, result);
        } else {
          executeJsMethod(callback, {
            'success': false,
            'response': {'code': 400, 'message': '用户退出页面', 'data': null},
          });
        }
      }
    } catch (e) {
      LogUtil.e('解析地址数据失败: $e');
      if (callback.isNotEmpty) {
        executeJsMethod(callback, {
          'success': false,
          'response': {'code': 400, 'message': '解析地址数据异常', 'data': null},
        });
      }
    }
  }
}
