import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../webview_bridge_interface.dart';

class ShowToastHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "showToast";

  ShowToastHandler(super.context, super.controller, {super.handlerFunc});

  @override
  void handler(JSMessage message) {
    Fluttertoast.showToast(
      msg: message.data["message"] ?? '',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
    );
  }

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }
}
