import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/cupertino.dart';
import '../webview_bridge_interface.dart';

class CloseHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "close";

  CloseHandler(super.context, super.controller, {super.handlerFunc});

  @override
  void handler(JSMessage message) {
    Navigator.pop(context);
  }

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }
}
