import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:flutter/material.dart';

import '../../pages/settings/AddressManagementPage.dart';
import '../webview_bridge_interface.dart';

class OpenAddressListHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "openAddressList";

  OpenAddressListHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) async {
    String callback = message.data["callback"] ?? '';

    var result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddressManagementPage(
          isSelectionMode: true, // 启用选择模式
        ),
      ),
    );

    if (callback.isNotEmpty) {
      if (result != null && result is Map<String, dynamic>) {
        executeJsMethod(callback, result);
      } else {
        executeJsMethod(callback, {
          'success': false,
          'response': {'code': 400, 'message': '用户退出页面', 'data': null},
        });
      }
    }
  }
}
