import 'package:dlyz_flutter/webview/js_bridge.dart';
import '../../pages/community/switch_bind_role.dart';
import '../webview_bridge_interface.dart';

class ShowGameBindingDialogHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "showGameBindingDialog";

  ShowGameBindingDialogHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) {
    String callback = message.data["callback"] ?? '';
    SwitchBindRoleDialog.showCharacterSwitchDialog(
      context,
      onCharacterSwitched: () {},
      onNavigateToBindCharacter: () {},
      onClose: () {
        if (callback.isNotEmpty) {
          final selectRole = SwitchBindRoleDialog.getSelectedRole(context);
          executeJsMethod(callback, {'selectRole': selectRole});
        }
      },
    );
  }
}
