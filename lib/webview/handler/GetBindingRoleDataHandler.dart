import 'package:dlyz_flutter/webview/js_bridge.dart';

import '../../pages/community/switch_bind_role.dart';
import '../webview_bridge_interface.dart';

class GetBindingRoleDataHandler extends IWebViewBridge {
  static final String BRIDGE_NAME = "getBindingRoleData";

  GetBindingRoleDataHandler(super.context, super.controller, {super.handlerFunc});

  @override
  String bridgeName() {
    return BRIDGE_NAME;
  }

  @override
  void handler(JSMessage message) {
    final roles = SwitchBindRoleDialog.getBindingRoles(context);
    String callback = message.data["callback"] ?? '';
    if (callback.isNotEmpty) {
      executeJsMethod(callback, {'roles': roles});
    }
  }
}
