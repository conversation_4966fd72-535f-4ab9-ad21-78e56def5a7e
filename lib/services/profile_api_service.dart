import '../model/user_detail.dart';
import '../model/game_circle.dart';
import '../model/token_response.dart';
import '../net/http_service.dart';
import '../net/http_base_response.dart';
import '../net/sign_interceptor.dart';
import '../net/config/http_base_config.dart';

class ProfileApiService {

  static final ProfileApiService _instance = ProfileApiService._internal();
  factory ProfileApiService() => _instance;
  ProfileApiService._internal();

  HttpService get _httpService => HttpService.getInstance();

  static const String _getUserDetailInfoPath = '/api/gamehub-api/v1/my_zone/get_detail_info';
  static const String _getPlayCirclePath = '/api/gamehub-api/v1/my_zone/get_play_circle';
  static const String _ticket2TokenPath = '/api/gamehub-api/v1/my_zone/ticket_to_token';

  /// 获取用户基本信息
  /// 
  /// [appTicket] 应用票据，必需参数
  /// 返回 [Future<BaseResponse<UserDetail>>] 用户基本信息
  Future<BaseResponse<UserDetail>> getUserDetailInfo(String ticket) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        "app_ticket": ticket,
      };

      // 发起请求（使用v3签名）
      return await _httpService.post<UserDetail>(
        _getUserDetailInfoPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => UserDetail.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 获取Play圈子
  /// 
  /// [appTicket] 应用票据，必需参数
  /// 返回 [Future<BaseResponse<GameCircleListResponse>>] Play圈子信息
  Future<BaseResponse<GameCircleListResponse>> getPlayCircle(String appTicket) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        "app_ticket": appTicket,
      };

      // 发起请求（使用v3签名）
      return await _httpService.post<GameCircleListResponse>(
        _getPlayCirclePath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return GameCircleListResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 票据换取Token
  /// 
  /// [appTicket] 应用票据，必需参数
  /// 返回 [Future<BaseResponse<TokenResponse>>] Token信息
  Future<BaseResponse<TokenResponse>> ticket2Token(String appTicket) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> params = {
        "app_ticket": appTicket,
      };

      // 发起请求（使用v3签名）
      return await _httpService.post<TokenResponse>(
        _ticket2TokenPath,
        baseUrl: HttpBaseConfig.baseUrl,
        data: params,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) => TokenResponse.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

}
  